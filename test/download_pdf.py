import boto3
import os


def download_all_pdfs(bucket_name, prefix="", local_dir="./downloads"):
    """
    Download all PDF files from an S3 bucket (optionally within a prefix) to a local directory.

    :param bucket_name: Name of the S3 bucket
    :param prefix: (Optional) Prefix/folder path in S3 bucket
    :param local_dir: Local directory to save the PDFs
    """
    s3 = boto3.client(
    "s3",
    aws_access_key_id="********************",
    aws_secret_access_key="FhokCEIEYjhPxSmuVtyH1BvWHizdYGNPDHgs3wc2",
    region_name="ap-south-1"
    )

    # Ensure local directory exists
    os.makedirs(local_dir, exist_ok=True)

    paginator = s3.get_paginator("list_objects_v2")
    page_iterator = paginator.paginate(Bucket=bucket_name, Prefix=prefix)

    pdf_count = 0

    for page in page_iterator:
        if "Contents" in page:
            for obj in page["Contents"]:
                key = obj["Key"]
                if key.lower().endswith(".pdf"):
                    local_path = os.path.join(local_dir, os.path.basename(key))
                    print(f"📥 Downloading {key} to {local_path}")
                    s3.download_file(bucket_name, key, local_path)
                    pdf_count += 1

    print(f"✅ Downloaded {pdf_count} PDF files to {local_dir}")

# Example usage
if __name__ == "__main__":
    bucket = "s3ahp"
    prefix = "ah/task"  # Leave "" if you want the entire bucket
    download_all_pdfs(bucket, prefix)
