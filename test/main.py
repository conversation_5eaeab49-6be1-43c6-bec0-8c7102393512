import fitz  # PyMuPDF
import psycopg2
from transformers import AutoTokenizer, AutoModel
import torch
from ollama import chat
import time
from datetime import datetime
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# --- CONFIG ---
DB_NAME = "kforce"
DB_USER = "postgres"
DB_PASS = "kforce"
OLLAMA_MODEL = "llama3"
GROK_API_KEY = "********************************************************"

# --- Nomic Embedder ---
class NomicEmbedder:
    def __init__(self):
        self.tokenizer = AutoTokenizer.from_pretrained(
            "nomic-ai/nomic-embed-text-v1", trust_remote_code=True
        )
        self.model = AutoModel.from_pretrained(
            "nomic-ai/nomic-embed-text-v1", trust_remote_code=True
        )
        self.model.eval()

    def encode(self, texts):
        with torch.no_grad():
            inputs = self.tokenizer(texts, padding=True, truncation=True, return_tensors="pt")
            outputs = self.model(**inputs)
            embeddings = outputs.last_hidden_state[:, 0, :]
            return embeddings.cpu().numpy().tolist()

# --- 4. Query Similar Chunks ---
def query_similar_chunks(question, embedder, top_k=10):
    query_vec = embedder.encode([question])[0]
    conn = psycopg2.connect(dbname=DB_NAME, user=DB_USER, password=DB_PASS,host="localhost",port=5444)
    cur = conn.cursor()
    cur.execute("""
    SELECT content FROM documents
    ORDER BY embedding <#> %s::vector
    LIMIT %s
    """, (query_vec, top_k))
    results = cur.fetchall()
    cur.close()
    conn.close()
    return [r[0] for r in results]

# --- 5. Ask Question to Ollama ---
def ask_ollama(question, context):
    prompt = f"""
    You are an expert assistant. Use the context below to answer the user's question.Don't recommend any other information other than the context provided.

    Context:
    {context}

    Question: {question}

    Answer:
    """
    response = chat(model="llama3.2", messages=[{"role": "user", "content": prompt}])
    return response['message']['content']

# --- MAIN PIPELINE ---
if __name__ == "__main__":
    # Initialize embedder
  
    embedder = NomicEmbedder()
    
    # Ask a question
    question = input("Enter your question: ")
    start_time = time.time()
    start_datetime = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"Starting query processing at: {start_datetime}")
    relevant_chunks = query_similar_chunks(question, embedder)
    context = "\n---\n".join(relevant_chunks)
    end_time = time.time()
    end_datetime = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"Finished end processing at: {end_datetime}")
    answer = ask_ollama(question, context)

    print("\nAnswer from Ollama:\n")
    print(answer)
