#!/usr/bin/env python3
"""
Comprehensive testing of the improved chunking strategy
"""

import os
import psycopg2
from dotenv import load_dotenv
from collections import Counter

# Load environment variables
load_dotenv()

def test_chunking_distribution():
    """Test the distribution of chunks across documents"""
    
    print("📊 Testing Chunking Distribution")
    print("=" * 50)
    
    try:
        conn = psycopg2.connect(
            dbname=os.getenv("DB_NAME", "kforce"),
            user=os.getenv("DB_USER", "postgres"),
            password=os.getenv("DB_PASS", "kforce"),
            host=os.getenv("DB_HOST", "localhost"),
            port=os.getenv("DB_PORT", "5444")
        )
        
        with conn.cursor() as cur:
            # Get total statistics
            cur.execute("SELECT COUNT(*) FROM documents")
            total_chunks = cur.fetchone()[0]
            
            cur.execute("SELECT COUNT(DISTINCT filename) FROM documents")
            unique_files = cur.fetchone()[0]
            
            print(f"📄 Total chunks: {total_chunks}")
            print(f"📁 Unique files: {unique_files}")
            
            if total_chunks == 0:
                print("⚠️  No documents processed yet. Processing is still running.")
                return False
            
            # Analyze chunk distribution per file
            cur.execute("""
                SELECT filename, COUNT(*) as chunk_count 
                FROM documents 
                GROUP BY filename 
                ORDER BY chunk_count DESC
                LIMIT 20
            """)
            
            file_chunks = cur.fetchall()
            
            # Get all chunk counts for distribution
            cur.execute("""
                SELECT COUNT(*) as chunk_count 
                FROM documents 
                GROUP BY filename
            """)
            
            chunk_counts = [row[0] for row in cur.fetchall()]
            distribution = Counter(chunk_counts)
            
            print(f"\n📈 Chunk Distribution:")
            for chunks, file_count in sorted(distribution.items()):
                percentage = (file_count / unique_files) * 100
                print(f"   {chunks} chunk(s): {file_count} files ({percentage:.1f}%)")
            
            # Statistics
            if chunk_counts:
                avg_chunks = sum(chunk_counts) / len(chunk_counts)
                print(f"\n📊 Statistics:")
                print(f"   Average chunks per file: {avg_chunks:.2f}")
                print(f"   Min chunks: {min(chunk_counts)}")
                print(f"   Max chunks: {max(chunk_counts)}")
            
            # Show examples
            print(f"\n📋 Examples of Different Chunk Counts:")
            for filename, count in file_chunks[:10]:
                print(f"   {count} chunks: {filename}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_chunk_quality():
    """Test the quality of individual chunks"""
    
    print(f"\n🔍 Testing Chunk Quality")
    print("=" * 30)
    
    try:
        conn = psycopg2.connect(
            dbname=os.getenv("DB_NAME", "kforce"),
            user=os.getenv("DB_USER", "postgres"),
            password=os.getenv("DB_PASS", "kforce"),
            host=os.getenv("DB_HOST", "localhost"),
            port=os.getenv("DB_PORT", "5444")
        )
        
        with conn.cursor() as cur:
            # Analyze word count distribution
            cur.execute("SELECT word_count FROM documents ORDER BY word_count")
            word_counts = [row[0] for row in cur.fetchall()]
            
            if not word_counts:
                print("⚠️  No chunks to analyze yet.")
                return False
            
            print(f"📝 Word Count Analysis:")
            print(f"   Total chunks analyzed: {len(word_counts)}")
            print(f"   Average words per chunk: {sum(word_counts)/len(word_counts):.1f}")
            print(f"   Min words: {min(word_counts)}")
            print(f"   Max words: {max(word_counts)}")
            
            # Quality thresholds
            very_small = sum(1 for wc in word_counts if wc < 10)
            small = sum(1 for wc in word_counts if 10 <= wc < 50)
            medium = sum(1 for wc in word_counts if 50 <= wc < 300)
            large = sum(1 for wc in word_counts if 300 <= wc < 800)
            very_large = sum(1 for wc in word_counts if wc >= 800)
            
            total = len(word_counts)
            
            print(f"\n📊 Chunk Size Distribution:")
            print(f"   Very small (< 10 words): {very_small} ({very_small/total*100:.1f}%)")
            print(f"   Small (10-49 words): {small} ({small/total*100:.1f}%)")
            print(f"   Medium (50-299 words): {medium} ({medium/total*100:.1f}%)")
            print(f"   Large (300-799 words): {large} ({large/total*100:.1f}%)")
            print(f"   Very large (800+ words): {very_large} ({very_large/total*100:.1f}%)")
            
            # Sample chunks
            print(f"\n📖 Sample Chunks:")
            
            # Get a sample chunk
            cur.execute("""
                SELECT filename, heading, word_count, content 
                FROM documents 
                WHERE word_count BETWEEN 100 AND 300 
                LIMIT 1
            """)
            result = cur.fetchone()
            if result:
                filename, heading, wc, content = result
                print(f"\n   📄 Sample Chunk ({wc} words):")
                print(f"      File: {filename}")
                print(f"      Heading: {heading}")
                print(f"      Content: {content[:150]}...")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_processing_progress():
    """Check processing progress"""
    
    print(f"\n⏱️  Checking Processing Progress")
    print("=" * 40)
    
    try:
        conn = psycopg2.connect(
            dbname=os.getenv("DB_NAME", "kforce"),
            user=os.getenv("DB_USER", "postgres"),
            password=os.getenv("DB_PASS", "kforce"),
            host=os.getenv("DB_HOST", "localhost"),
            port=os.getenv("DB_PORT", "5444")
        )
        
        with conn.cursor() as cur:
            # Get current progress
            cur.execute("SELECT COUNT(DISTINCT filename) FROM documents")
            processed_files = cur.fetchone()[0]
            
            # Estimate total files
            from pathlib import Path
            training_path = os.getenv("TRAINING_DATA_PATH", "pdf")
            pdf_folder = Path(training_path)
            
            if pdf_folder.exists():
                total_files = len(list(pdf_folder.glob("*.pdf")))
                progress_percent = (processed_files / total_files) * 100
                
                print(f"📊 Processing Progress:")
                print(f"   Processed: {processed_files:,} files")
                print(f"   Total: {total_files:,} files")
                print(f"   Progress: {progress_percent:.1f}%")
                
                if progress_percent < 100:
                    remaining = total_files - processed_files
                    print(f"   Remaining: {remaining:,} files")
                    print(f"   Status: 🔄 Still processing...")
                else:
                    print(f"   Status: ✅ Processing complete!")
                
                return progress_percent >= 100
            else:
                print(f"❌ Training folder not found: {training_path}")
                return False
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error checking progress: {e}")
        return False

def test_search_functionality():
    """Test basic search functionality"""
    
    print(f"\n🔍 Testing Search Functionality")
    print("=" * 40)
    
    try:
        # Simple database query test
        conn = psycopg2.connect(
            dbname=os.getenv("DB_NAME", "kforce"),
            user=os.getenv("DB_USER", "postgres"),
            password=os.getenv("DB_PASS", "kforce"),
            host=os.getenv("DB_HOST", "localhost"),
            port=os.getenv("DB_PORT", "5444")
        )
        
        with conn.cursor() as cur:
            # Test basic text search
            test_terms = ["payment", "amount", "total", "agreement", "address"]
            
            print(f"🧪 Testing text search for common terms:")
            
            for term in test_terms:
                cur.execute("""
                    SELECT COUNT(*) 
                    FROM documents 
                    WHERE LOWER(content) LIKE %s
                """, (f'%{term.lower()}%',))
                
                count = cur.fetchone()[0]
                print(f"   '{term}': {count} chunks contain this term")
                
                if count > 0:
                    # Show a sample
                    cur.execute("""
                        SELECT filename, heading, word_count 
                        FROM documents 
                        WHERE LOWER(content) LIKE %s
                        LIMIT 1
                    """, (f'%{term.lower()}%',))
                    
                    result = cur.fetchone()
                    if result:
                        filename, heading, wc = result
                        print(f"      Sample: {filename} - {heading} ({wc} words)")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Search test error: {e}")
        return False

def run_comprehensive_tests():
    """Run all tests"""
    
    print("🧪 COMPREHENSIVE CHUNKING STRATEGY TESTS")
    print("=" * 60)
    
    # Check processing progress first
    processing_complete = test_processing_progress()
    
    # Test chunking distribution
    distribution_ok = test_chunking_distribution()
    
    if distribution_ok:
        # Test chunk quality
        quality_ok = test_chunk_quality()
        
        # Test search functionality
        search_ok = test_search_functionality()
        
        # Summary
        print(f"\n📋 TEST SUMMARY")
        print("=" * 30)
        print(f"   Processing Complete: {'✅' if processing_complete else '🔄'}")
        print(f"   Chunking Distribution: {'✅' if distribution_ok else '❌'}")
        print(f"   Chunk Quality: {'✅' if quality_ok else '❌'}")
        print(f"   Search Functionality: {'✅' if search_ok else '❌'}")
        
        if all([distribution_ok, quality_ok, search_ok]):
            print(f"\n🎉 All tests passed! Improved chunking strategy is working excellently!")
            
            if not processing_complete:
                print(f"💡 Processing is still running. Full benefits will be available when complete.")
        else:
            print(f"\n⚠️  Some tests need attention.")
    else:
        print(f"\n⏳ Processing still in progress. Run tests again when complete.")

if __name__ == "__main__":
    run_comprehensive_tests()
