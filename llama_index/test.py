from llama_index.core import Settings, VectorStoreIndex, SimpleDirectoryReader
from llama_index.vector_stores.postgres import PGVectorStore
from llama_index.embeddings.huggingface import HuggingFaceEmbedding
from llama_index.llms.ollama import Ollama
import ollama
from transformers import AutoTokenizer
from ollama import chat
from llama_index.multi_modal_llms.openai import OpenAIMultiModal

# Update the embedding model initialization with trust_remote_code=True
embedding_model = HuggingFaceEmbedding(
    model_name="nomic-ai/nomic-embed-text-v1",
    trust_remote_code=True
) 

# Set the embedding model in Settings
Settings.embed_model = embedding_model
Settings.llm = Ollama(model="llama3", base_url="http://localhost:11434", request_timeout=120.0)

# Create a simple chat message


# Load documents and create index
documents = SimpleDirectoryReader("./pdfs").load_data()
index = VectorStoreIndex.from_documents(documents)

print(index)