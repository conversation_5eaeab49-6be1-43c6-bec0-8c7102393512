#!/usr/bin/env python3
"""
Complete improvement pipeline: analyze, reindex, and test
"""

import subprocess
import sys
import os

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n{'='*60}")
    print(f"🚀 {description}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        
        if result.stdout:
            print(result.stdout)
        
        if result.stderr:
            print(f"⚠️ Warnings/Errors:")
            print(result.stderr)
        
        if result.returncode != 0:
            print(f"❌ Command failed with return code: {result.returncode}")
            return False
        else:
            print(f"✅ {description} completed successfully!")
            return True
            
    except Exception as e:
        print(f"❌ Error running command: {str(e)}")
        return False

def main():
    """Run the complete improvement pipeline"""
    
    print("🔧 COMPLETE RAG SYSTEM IMPROVEMENT PIPELINE")
    print("=" * 60)
    print("This script will:")
    print("1. Analyze existing documents")
    print("2. Create improved indexing")
    print("3. Test the new system")
    print("4. Compare with original results")
    
    # Check if required files exist
    required_files = [
        "analyze_documents.py",
        "improved_indexing.py", 
        "test_improved_search.py",
        "main.llamaindex.py"
    ]
    
    missing_files = [f for f in required_files if not os.path.exists(f)]
    if missing_files:
        print(f"❌ Missing required files: {missing_files}")
        return
    
    print(f"\n✅ All required files found")
    
    # Step 1: Analyze documents
    print(f"\n📊 STEP 1: Analyzing Documents")
    success = run_command("python analyze_documents.py", "Document Analysis")
    if not success:
        print("❌ Document analysis failed. Please check the output above.")
        return
    
    # Step 2: Create improved indexing
    print(f"\n🔄 STEP 2: Creating Improved Index")
    success = run_command("python improved_indexing.py", "Improved Indexing")
    if not success:
        print("❌ Improved indexing failed. Please check the output above.")
        return
    
    # Step 3: Test improved search
    print(f"\n🧪 STEP 3: Testing Improved Search")
    success = run_command("python test_improved_search.py", "Testing Improved Search")
    if not success:
        print("❌ Testing failed. Please check the output above.")
        return
    
    # Step 4: Final recommendations
    print(f"\n💡 STEP 4: Final Recommendations")
    print("=" * 60)
    
    print(f"✅ Improvement pipeline completed!")
    print(f"\n📋 What was done:")
    print(f"1. ✅ Analyzed all PDF documents for content and key terms")
    print(f"2. ✅ Created enhanced indexing with multiple chunk types:")
    print(f"   - Full documents for comprehensive context")
    print(f"   - Title-enhanced chunks for direct disease matching")
    print(f"   - Symptoms-focused chunks for symptom-based search")
    print(f"   - Sentence-level chunks for fine-grained matching")
    print(f"3. ✅ Updated main system to use improved index")
    print(f"4. ✅ Tested with problematic query variations")
    
    print(f"\n🎯 Next Steps:")
    print(f"1. Review the test results above")
    print(f"2. If MILK FEVER is now found: Success! Use the improved system")
    print(f"3. If still having issues:")
    print(f"   - Check if MILK FEVER.pdf contains the expected symptoms")
    print(f"   - Consider using different embedding models")
    print(f"   - Adjust similarity thresholds")
    
    print(f"\n🚀 To use the improved system:")
    print(f"   python main.llamaindex.py")
    print(f"   Then test: 'Lateral recumbency with curved neck on lateral side of body'")
    
    print(f"\n📊 To compare old vs new:")
    print(f"   - Old table: 'documents'")
    print(f"   - New table: 'documents_improved'")
    print(f"   - Switch DB_TABLE in main.llamaindex.py to compare")

if __name__ == "__main__":
    main()
