#!/usr/bin/env python3
"""
Improved indexing with better chunking strategies for medical documents
"""

import os
import re
from datetime import datetime
from llama_index.readers.file import PyMuPDFReader
from llama_index.vector_stores.postgres import PGVectorStore
from llama_index.core import Document, VectorStoreIndex, StorageContext
from llama_index.core.node_parser import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ter
from transformers import AutoTokenizer, AutoModel
from llama_index.core.embeddings import BaseEmbedding
import torch

# --- Custom Nomic Embedder ---
class NomicEmbedding(BaseEmbedding):
    def __init__(self, model_name="nomic-ai/nomic-embed-text-v1", **kwargs):
        super().__init__(**kwargs)
        # Use object.__setattr__ to bypass Pydantic validation
        object.__setattr__(self, 'tokenizer', AutoTokenizer.from_pretrained(model_name, trust_remote_code=True))
        object.__setattr__(self, 'model', AutoModel.from_pretrained(model_name, trust_remote_code=True))
        self.model.eval()

    class Config:
        arbitrary_types_allowed = True

    def _get_embedding(self, text: str):
        with torch.no_grad():
            inputs = self.tokenizer(text, padding=True, truncation=True, return_tensors="pt")
            outputs = self.model(**inputs)
            return outputs.last_hidden_state[:, 0, :].squeeze(0).cpu().tolist()

    def _get_text_embedding(self, text: str):
        """Get embedding for a single text"""
        return self._get_embedding(text)

    def _get_text_embeddings(self, texts):
        """Get embeddings for multiple texts"""
        return [self._get_embedding(text) for text in texts]

    def _get_query_embedding(self, query: str):
        """Get embedding for a query"""
        return self._get_embedding(query)

    async def _aget_query_embedding(self, query: str):
        """Async version of get_query_embedding"""
        return self._get_query_embedding(query)

# --- CONFIG ---
PDF_FOLDER = "diseases"
DB_NAME = "kforce"
DB_USER = "postgres"
DB_PASS = "kforce"
DB_HOST = "localhost"
DB_PORT = "5444"
DB_TABLE = "documents_improved"  # New table for improved indexing

def extract_medical_sections(text):
    """Extract different sections from medical documents"""
    
    sections = {
        'title': '',
        'symptoms': '',
        'diagnosis': '',
        'treatment': '',
        'causes': '',
        'full_text': text
    }
    
    # Extract title (usually first line or in caps)
    lines = text.split('\n')
    for line in lines[:5]:
        if line.strip() and (line.isupper() or len(line.strip()) < 100):
            sections['title'] = line.strip()
            break
    
    # Extract symptoms/clinical signs section
    symptom_patterns = [
        r'(?:symptoms?|clinical\s+signs?|signs?|manifestations?)\s*:?\s*\n(.*?)(?:\n\s*\n|\n[A-Z][^a-z]*:|\Z)',
        r'(?:symptoms?|clinical\s+signs?|signs?)\s*(?:and|&)\s*(?:symptoms?|clinical\s+signs?|signs?)\s*:?\s*\n(.*?)(?:\n\s*\n|\n[A-Z][^a-z]*:|\Z)'
    ]
    
    for pattern in symptom_patterns:
        matches = re.findall(pattern, text, re.IGNORECASE | re.DOTALL)
        if matches:
            sections['symptoms'] = matches[0].strip()
            break
    
    # Extract diagnosis section
    diagnosis_patterns = [
        r'(?:diagnosis|diagnostic)\s*:?\s*\n(.*?)(?:\n\s*\n|\n[A-Z][^a-z]*:|\Z)',
    ]
    
    for pattern in diagnosis_patterns:
        matches = re.findall(pattern, text, re.IGNORECASE | re.DOTALL)
        if matches:
            sections['diagnosis'] = matches[0].strip()
            break
    
    # Extract treatment section
    treatment_patterns = [
        r'(?:treatment|therapy|management)\s*:?\s*\n(.*?)(?:\n\s*\n|\n[A-Z][^a-z]*:|\Z)',
    ]
    
    for pattern in treatment_patterns:
        matches = re.findall(pattern, text, re.IGNORECASE | re.DOTALL)
        if matches:
            sections['treatment'] = matches[0].strip()
            break
    
    # Extract causes/etiology section
    cause_patterns = [
        r'(?:causes?|etiology|pathogenesis)\s*:?\s*\n(.*?)(?:\n\s*\n|\n[A-Z][^a-z]*:|\Z)',
    ]
    
    for pattern in cause_patterns:
        matches = re.findall(pattern, text, re.IGNORECASE | re.DOTALL)
        if matches:
            sections['causes'] = matches[0].strip()
            break
    
    return sections

def create_enhanced_documents(pdf_files):
    """Create enhanced documents with better chunking and metadata"""
    
    reader = PyMuPDFReader()
    all_docs = []
    
    print(f"\n📚 Processing {len(pdf_files)} documents with enhanced chunking...")
    
    for i, pdf_file in enumerate(pdf_files, 1):
        file_path = os.path.join(PDF_FOLDER, pdf_file)
        print(f"[{i}/{len(pdf_files)}] Processing: {pdf_file}")
        
        try:
            raw_docs = reader.load_data(file_path=file_path)
            combined_text = "\n\n".join([doc.text.strip() for doc in raw_docs if doc.text.strip()])
            
            if not combined_text:
                print(f"   ⚠️ No valid text found in {pdf_file}")
                continue
            
            # Extract medical sections
            sections = extract_medical_sections(combined_text)
            
            # Create multiple documents for different sections
            base_metadata = {
                "source": pdf_file,
                "total_pages": len(raw_docs),
                "processed_at": datetime.now().isoformat(),
                "indexing_strategy": "enhanced_medical"
            }
            
            # 1. Full document (for comprehensive context)
            full_doc = Document(
                text=combined_text,
                metadata={
                    **base_metadata,
                    "section_type": "full_document",
                    "chunk_type": "complete"
                }
            )
            all_docs.append(full_doc)
            
            # 2. Title/Disease name document (for direct matching)
            if sections['title']:
                title_doc = Document(
                    text=f"Disease: {sections['title']}\n\nFull content: {combined_text[:500]}...",
                    metadata={
                        **base_metadata,
                        "section_type": "title",
                        "chunk_type": "title_enhanced",
                        "disease_name": sections['title']
                    }
                )
                all_docs.append(title_doc)
            
            # 3. Symptoms-focused document (most important for search)
            if sections['symptoms']:
                symptoms_enhanced = f"Symptoms and Clinical Signs of {sections['title'] or pdf_file.replace('.pdf', '')}:\n\n{sections['symptoms']}"
                
                symptoms_doc = Document(
                    text=symptoms_enhanced,
                    metadata={
                        **base_metadata,
                        "section_type": "symptoms",
                        "chunk_type": "symptoms_focused",
                        "disease_name": sections['title'] or pdf_file.replace('.pdf', '')
                    }
                )
                all_docs.append(symptoms_doc)
            
            # 4. Diagnosis document
            if sections['diagnosis']:
                diagnosis_doc = Document(
                    text=f"Diagnosis of {sections['title'] or pdf_file.replace('.pdf', '')}:\n\n{sections['diagnosis']}",
                    metadata={
                        **base_metadata,
                        "section_type": "diagnosis",
                        "chunk_type": "diagnosis_focused"
                    }
                )
                all_docs.append(diagnosis_doc)
            
            # 5. Treatment document
            if sections['treatment']:
                treatment_doc = Document(
                    text=f"Treatment of {sections['title'] or pdf_file.replace('.pdf', '')}:\n\n{sections['treatment']}",
                    metadata={
                        **base_metadata,
                        "section_type": "treatment", 
                        "chunk_type": "treatment_focused"
                    }
                )
                all_docs.append(treatment_doc)
            
            # 6. Sentence-level chunks for fine-grained search
            splitter = SentenceSplitter(chunk_size=300, chunk_overlap=50)
            sentence_chunks = splitter.split_text(combined_text)
            
            for j, chunk in enumerate(sentence_chunks):
                chunk_doc = Document(
                    text=chunk,
                    metadata={
                        **base_metadata,
                        "section_type": "sentence_chunk",
                        "chunk_type": "sentence_split",
                        "chunk_index": j,
                        "total_chunks": len(sentence_chunks)
                    }
                )
                all_docs.append(chunk_doc)
            
            print(f"   ✅ Created {1 + len([s for s in sections.values() if s]) + len(sentence_chunks)} documents")
            
        except Exception as e:
            print(f"   ❌ Error processing {pdf_file}: {str(e)}")
    
    return all_docs

def improved_indexing():
    """Perform improved indexing with enhanced chunking"""
    
    print("🚀 Starting Improved Medical Document Indexing")
    print("=" * 60)
    
    # Check folder exists
    if not os.path.exists(PDF_FOLDER):
        print(f"❌ Folder not found: {PDF_FOLDER}")
        return
    
    pdf_files = [f for f in os.listdir(PDF_FOLDER) if f.lower().endswith(".pdf")]
    if not pdf_files:
        print(f"❌ No PDF files found in {PDF_FOLDER}")
        return
    
    print(f"📚 Found {len(pdf_files)} PDF files")
    
    # Initialize components
    print(f"\n🔧 Initializing components...")
    embed_model = NomicEmbedding()
    
    # Create new vector store for improved indexing
    vector_store = PGVectorStore.from_params(
        database=DB_NAME,
        user=DB_USER,
        password=DB_PASS,
        host=DB_HOST,
        port=int(DB_PORT),
        table_name=DB_TABLE,
        embed_dim=768
    )
    
    storage_context = StorageContext.from_defaults(vector_store=vector_store)
    
    # Create enhanced documents
    all_docs = create_enhanced_documents(pdf_files)
    
    if not all_docs:
        print("❌ No valid documents created. Exiting.")
        return
    
    # Index documents
    print(f"\n📡 Indexing {len(all_docs)} enhanced documents...")
    index = VectorStoreIndex(
        all_docs,
        embed_model=embed_model,
        storage_context=storage_context,
    )
    
    print(f"\n✅ Improved indexing completed!")
    print(f"📊 Statistics:")
    print(f"   Total documents indexed: {len(all_docs)}")
    print(f"   Original PDF files: {len(pdf_files)}")
    print(f"   Average documents per PDF: {len(all_docs) / len(pdf_files):.1f}")
    print(f"   Database table: {DB_TABLE}")
    
    # Show breakdown by chunk type
    chunk_types = {}
    for doc in all_docs:
        chunk_type = doc.metadata.get('chunk_type', 'unknown')
        chunk_types[chunk_type] = chunk_types.get(chunk_type, 0) + 1
    
    print(f"\n📋 Document breakdown by type:")
    for chunk_type, count in sorted(chunk_types.items()):
        print(f"   {chunk_type}: {count} documents")

if __name__ == "__main__":
    improved_indexing()
    
    print(f"\n🎯 NEXT STEPS:")
    print("=" * 60)
    print("1. Update main.llamaindex.py to use the new table")
    print("2. Test with: python test_improved_search.py")
    print("3. Compare results with the original indexing")
