#!/usr/bin/env python3
"""
Simple test of the improved chunking strategy
"""

import sys
import traceback

def test_chunking():
    """Simple chunking test"""
    
    try:
        print("🧪 Simple Chunking Test")
        print("=" * 30)
        
        # Import the processor
        from pdf_embedder import PDFProcessor
        
        print("✅ Successfully imported PDFProcessor")
        
        # Create processor
        processor = PDFProcessor()
        print("✅ Successfully created processor")
        
        # Test with sample text
        sample_text = """
# ACIDOSIS

## Symptoms
- Decreased appetite
- Lethargy
- Rapid breathing
- Dehydration

## Treatment
- Fluid therapy
- Sodium bicarbonate
- Supportive care

## Prevention
- Proper nutrition
- Regular monitoring
"""
        
        print("🔧 Testing with sample text...")
        chunks = processor.chunk_text(sample_text, "test.pdf")
        
        print(f"✅ Created {len(chunks)} chunks")
        
        for i, chunk in enumerate(chunks, 1):
            print(f"\nChunk {i}:")
            print(f"  Heading: {chunk['heading']}")
            print(f"  Words: {chunk['word_count']}")
            print(f"  Content: {chunk['text'][:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        print("Traceback:")
        traceback.print_exc()
        return False

def test_with_real_pdf():
    """Test with a real PDF"""
    
    try:
        print(f"\n📄 Testing with real PDF")
        print("=" * 30)
        
        from pdf_embedder import PDFProcessor
        
        processor = PDFProcessor()
        
        # Try to extract text from ACIDOSIS.pdf
        pdf_path = "diseases/ACIDOSIS.pdf"
        text = processor.extract_text_from_pdf(pdf_path)
        
        if text:
            print(f"✅ Extracted {len(text)} characters from PDF")
            print(f"First 200 chars: {text[:200]}...")
            
            # Try chunking
            chunks = processor.chunk_text(text, "ACIDOSIS.pdf")
            print(f"✅ Created {len(chunks)} chunks from PDF")
            
            for i, chunk in enumerate(chunks[:3], 1):  # Show first 3 chunks
                print(f"\nChunk {i}:")
                print(f"  Heading: {chunk['heading']}")
                print(f"  Words: {chunk['word_count']}")
        else:
            print("❌ No text extracted from PDF")
            
    except Exception as e:
        print(f"❌ Error with PDF: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    print("🚀 Starting Chunking Tests")
    
    # Test 1: Simple text chunking
    success1 = test_chunking()
    
    # Test 2: Real PDF chunking
    if success1:
        test_with_real_pdf()
    
    print(f"\n{'✅ Tests completed!' if success1 else '❌ Tests failed!'}")
