#!/usr/bin/env python3
"""
Analyze all documents to understand content and improve indexing strategy
"""

import os
import sys
from datetime import datetime
from llama_index.readers.file import PyMuPDFReader
import re

def analyze_all_documents():
    """Analyze all PDF documents in the diseases folder"""
    
    print("🔍 Analyzing All Documents for Better Indexing")
    print("=" * 60)
    
    diseases_folder = "diseases"
    if not os.path.exists(diseases_folder):
        print(f"❌ Folder not found: {diseases_folder}")
        return
    
    pdf_files = [f for f in os.listdir(diseases_folder) if f.lower().endswith(".pdf")]
    
    if not pdf_files:
        print(f"❌ No PDF files found in {diseases_folder}")
        return
    
    print(f"📚 Found {len(pdf_files)} PDF files")
    
    reader = PyMuPDFReader()
    document_analysis = {}
    
    # Key medical terms to search for
    key_terms = [
        "lateral recumbency", "recumbency", "lying down", "unable to stand",
        "curved neck", "neck curvature", "twisted neck", "neck deviation",
        "hypocalcemia", "milk fever", "calcium deficiency", "low calcium",
        "paralysis", "weakness", "paresis", "prostrate",
        "postpartum", "after calving", "parturition", "calving",
        "symptoms", "signs", "clinical signs", "diagnosis", "treatment"
    ]
    
    for i, pdf_file in enumerate(pdf_files, 1):
        print(f"\n[{i}/{len(pdf_files)}] Analyzing: {pdf_file}")
        
        try:
            file_path = os.path.join(diseases_folder, pdf_file)
            documents = reader.load_data(file_path=file_path)
            
            # Combine all text
            full_text = "\n".join([doc.text for doc in documents if doc.text.strip()])
            
            # Basic stats
            word_count = len(full_text.split())
            char_count = len(full_text)
            
            # Find key terms
            found_terms = {}
            for term in key_terms:
                count = len(re.findall(r'\b' + re.escape(term.lower()) + r'\b', full_text.lower()))
                if count > 0:
                    found_terms[term] = count
            
            # Extract symptoms/signs sections
            symptoms_text = extract_symptoms_section(full_text)
            
            document_analysis[pdf_file] = {
                'pages': len(documents),
                'word_count': word_count,
                'char_count': char_count,
                'found_terms': found_terms,
                'symptoms_text': symptoms_text,
                'full_text': full_text
            }
            
            print(f"   📄 Pages: {len(documents)}, Words: {word_count}")
            print(f"   🔍 Key terms found: {len(found_terms)}")
            
            if found_terms:
                top_terms = sorted(found_terms.items(), key=lambda x: x[1], reverse=True)[:3]
                print(f"   🎯 Top terms: {', '.join([f'{term}({count})' for term, count in top_terms])}")
            
        except Exception as e:
            print(f"   ❌ Error: {str(e)}")
            document_analysis[pdf_file] = {'error': str(e)}
    
    # Special focus on MILK FEVER
    print(f"\n🎯 DETAILED ANALYSIS - MILK FEVER.pdf")
    print("=" * 60)
    
    if "MILK FEVER.pdf" in document_analysis:
        milk_fever_data = document_analysis["MILK FEVER.pdf"]
        
        if 'error' not in milk_fever_data:
            print(f"📊 Statistics:")
            print(f"   Pages: {milk_fever_data['pages']}")
            print(f"   Words: {milk_fever_data['word_count']}")
            print(f"   Characters: {milk_fever_data['char_count']}")
            
            print(f"\n🔍 Key Terms Found:")
            found_terms = milk_fever_data['found_terms']
            if found_terms:
                for term, count in sorted(found_terms.items(), key=lambda x: x[1], reverse=True):
                    print(f"   ✅ '{term}': {count} occurrences")
            else:
                print(f"   ❌ No key terms found!")
            
            # Show symptoms section
            symptoms = milk_fever_data['symptoms_text']
            if symptoms:
                print(f"\n📝 Symptoms Section:")
                print(f"   {symptoms[:300]}...")
            else:
                print(f"\n📝 No clear symptoms section found")
            
            # Show first 500 characters
            full_text = milk_fever_data['full_text']
            print(f"\n📖 Document Preview:")
            print(f"   {full_text[:500]}...")
            
            # Check for lateral recumbency specifically
            lateral_matches = re.findall(r'.{0,50}lateral.{0,50}', full_text.lower())
            recumbency_matches = re.findall(r'.{0,50}recumbency.{0,50}', full_text.lower())
            
            print(f"\n🔍 Specific Term Analysis:")
            print(f"   'lateral' contexts: {len(lateral_matches)}")
            for match in lateral_matches[:3]:
                print(f"      ...{match.strip()}...")
            
            print(f"   'recumbency' contexts: {len(recumbency_matches)}")
            for match in recumbency_matches[:3]:
                print(f"      ...{match.strip()}...")
        else:
            print(f"❌ Error analyzing MILK FEVER.pdf: {milk_fever_data['error']}")
    else:
        print(f"❌ MILK FEVER.pdf not found!")
    
    # Generate recommendations
    print(f"\n💡 INDEXING RECOMMENDATIONS:")
    print("=" * 60)
    
    # Find documents with most relevant terms
    relevant_docs = []
    for doc_name, data in document_analysis.items():
        if 'found_terms' in data and data['found_terms']:
            relevance_score = sum(data['found_terms'].values())
            relevant_docs.append((doc_name, relevance_score, data['found_terms']))
    
    relevant_docs.sort(key=lambda x: x[1], reverse=True)
    
    print(f"📊 Most Relevant Documents (by key term frequency):")
    for i, (doc_name, score, terms) in enumerate(relevant_docs[:5], 1):
        print(f"   {i}. {doc_name} (Score: {score})")
        top_terms = sorted(terms.items(), key=lambda x: x[1], reverse=True)[:3]
        print(f"      Top terms: {', '.join([f'{term}({count})' for term, count in top_terms])}")
    
    return document_analysis

def extract_symptoms_section(text):
    """Extract symptoms/signs section from document text"""
    
    # Common section headers for symptoms
    symptom_patterns = [
        r'symptoms?\s*:?\s*\n(.*?)(?:\n\s*\n|\n[A-Z][^a-z]*:|\Z)',
        r'clinical\s+signs?\s*:?\s*\n(.*?)(?:\n\s*\n|\n[A-Z][^a-z]*:|\Z)',
        r'signs?\s*:?\s*\n(.*?)(?:\n\s*\n|\n[A-Z][^a-z]*:|\Z)',
        r'manifestations?\s*:?\s*\n(.*?)(?:\n\s*\n|\n[A-Z][^a-z]*:|\Z)',
    ]
    
    for pattern in symptom_patterns:
        matches = re.findall(pattern, text, re.IGNORECASE | re.DOTALL)
        if matches:
            return matches[0].strip()
    
    return ""

if __name__ == "__main__":
    analysis = analyze_all_documents()
    
    print(f"\n🎯 NEXT STEPS:")
    print("=" * 60)
    print("1. Review the analysis above")
    print("2. Run: python improved_indexing.py")
    print("3. Test the new index with: python test_milk_fever_search.py")
