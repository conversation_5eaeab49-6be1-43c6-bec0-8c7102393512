# Medical Document Search System

A comprehensive PDF embedding and search system using Nomic embeddings, PostgreSQL vector storage, and Grok LLM for intelligent medical document retrieval and question answering.

## Features

- 📄 **PDF Processing**: Extract and chunk text from medical PDFs
- 🧠 **Smart Embeddings**: Use nomic-ai/nomic-embed-text-v1 via Ollama
- 🗄️ **Vector Storage**: Store embeddings in PostgreSQL with pgvector
- 🔍 **Semantic Search**: Find relevant documents using vector similarity
- 🤖 **AI Responses**: Generate answers using Grok LLM (Llama 4 Maverick)
- 💬 **Interactive Interface**: Command-line chat interface

## Prerequisites

1. **Python 3.8+**
2. **PostgreSQL** with pgvector extension
3. **Ollama** running locally with nomic-embed-text-v1 model
4. **Grok API Key** for LLM responses

## Quick Start

### 1. Setup Environment

```bash
# Run the setup script to check all dependencies
python setup.py
```

This will:
- Check Python version
- Install required packages
- Verify Ollama is running
- Test PostgreSQL connection
- Validate Grok API key
- Check for PDF documents

### 2. Install Ollama Model

If not already installed:
```bash
ollama pull nomic-embed-text-v1
```

### 3. Setup Database and Process PDFs

```bash
# Process all PDFs in the diseases folder and create embeddings
python main_app.py --setup
```

### 4. Start Search Interface

```bash
# Launch interactive search
python main_app.py
```

## Configuration

The system uses a `.env` file for configuration:

```env
# Database Configuration
DB_NAME=kforce
DB_USER=postgres
DB_PASS=kforce
DB_HOST=localhost
DB_PORT=5444

# API Keys
GROK_API_KEY=********************************************************

# Ollama Configuration
OLLAMA_BASE_URL=http://localhost:11434
EMBEDDING_MODEL=nomic-ai/nomic-embed-text-v1
```

## Usage Examples

### Command Line Options

```bash
# Setup database and process PDFs
python main_app.py --setup

# Test with single PDF
python main_app.py --test

# Show database information
python main_app.py --info

# Run interactive search (default)
python main_app.py
```

### Interactive Search

```
🔍 Enter your medical question: What are the symptoms of acidosis in cattle?

📋 Found 8 relevant documents

🤖 AI Response:
Based on the medical documents, acidosis in cattle presents with several key symptoms:

**Clinical Signs:**
- Decreased appetite and feed intake
- Lethargy and depression
- Diarrhea (often watery)
- Dehydration
- Increased heart rate
- Rapid, shallow breathing
- Weakness and reluctance to move

**Severe Cases May Show:**
- Ataxia (loss of coordination)
- Recumbency (inability to stand)
- Shock
- Death if untreated

**Laboratory Findings:**
- Low blood pH (< 7.35)
- Decreased bicarbonate levels
- Elevated lactate levels

📚 Sources used:
  1. ACIDOSIS.pdf (relevance: 95.2%)
  2. METABOLIC_DISORDERS.pdf (relevance: 87.3%)
```

## File Structure

```
├── main_app.py           # Main application entry point
├── pdf_embedder.py       # PDF processing and embedding
├── search_system.py      # Search engine and Grok LLM integration
├── setup.py             # Environment setup and validation
├── requirements.txt     # Python dependencies
├── .env                 # Configuration file
├── diseases/            # PDF documents folder
└── README.md           # This file
```

## System Architecture

1. **PDF Processing** (`pdf_embedder.py`)
   - Extract text from PDFs using PyMuPDF
   - Split into chunks with overlap for better context
   - Generate embeddings using Ollama + nomic-embed-text-v1

2. **Vector Storage** (PostgreSQL + pgvector)
   - Store document chunks with metadata
   - Index embeddings for fast similarity search
   - Support for exact and semantic search

3. **Search Engine** (`search_system.py`)
   - Vector similarity search using cosine distance
   - Retrieve top-k most relevant documents
   - Combine multiple sources for comprehensive context

4. **LLM Integration** (Grok API)
   - Send query + context to Grok LLM
   - Generate medical expert responses
   - Include source attribution and disclaimers

## Troubleshooting

### Common Issues

1. **Ollama Connection Error**
   ```bash
   # Check if Ollama is running
   curl http://localhost:11434/api/tags
   
   # Start Ollama if needed
   ollama serve
   ```

2. **PostgreSQL Connection Error**
   - Verify database credentials in `.env`
   - Ensure PostgreSQL is running on specified port
   - Check if pgvector extension is installed

3. **No PDFs Found**
   - Ensure PDF files are in the `diseases/` folder
   - Check file permissions

4. **Grok API Error**
   - Verify API key in `.env` file
   - Check internet connection
   - Ensure API key has sufficient credits

### Performance Tips

- **Chunk Size**: Adjust `chunk_size` in `PDFProcessor` for better retrieval
- **Top-K Results**: Modify `top_k` parameter for more/fewer search results
- **Database Indexing**: Ensure vector indexes are created for fast search

## Development

### Adding New Features

1. **Custom Embeddings**: Modify `NomicEmbedder` class
2. **Different LLMs**: Extend `GrokLLM` class
3. **New Document Types**: Update `PDFProcessor` class
4. **Advanced Search**: Enhance `VectorSearchEngine` class

### Testing

```bash
# Test single PDF processing
python main_app.py --test

# Check database status
python main_app.py --info

# Validate environment
python setup.py
```

## License

This project is for educational and research purposes. Please ensure compliance with your organization's data handling policies when processing medical documents.

## Support

For issues and questions:
1. Check the troubleshooting section
2. Verify all prerequisites are met
3. Run `python setup.py` to validate environment
