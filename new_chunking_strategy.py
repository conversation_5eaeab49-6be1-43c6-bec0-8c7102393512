#!/usr/bin/env python3
"""
New Improved Chunking Strategy for Medical Documents
Based on analysis of veterinary PDF structure
"""

import re
import hashlib
from typing import List, Dict
import fitz

class MedicalDocumentChunker:
    """Improved chunking strategy specifically for medical documents"""
    
    def __init__(self, base_chunk_size: int = 800):
        self.base_chunk_size = base_chunk_size
        
        # Medical section keywords based on veterinary document analysis
        self.medical_keywords = [
            'symptoms', 'signs', 'clinical signs', 'clinical symptoms',
            'treatment', 'therapy', 'management', 'medication',
            'diagnosis', 'diagnostic', 'differential diagnosis',
            'occurrence', 'epidemiology', 'prevalence', 'incidence',
            'prevention', 'prophylaxis', 'control measures',
            'etiology', 'causes', 'causative agent', 'pathogen',
            'pathogenesis', 'pathology', 'pathophysiology',
            'prognosis', 'outcome', 'recovery',
            'overview', 'introduction', 'definition', 'description'
        ]
    
    def extract_text_from_pdf(self, pdf_path: str) -> str:
        """Extract text from PDF"""
        try:
            doc = fitz.open(pdf_path)
            text = ""
            for page in doc:
                text += page.get_text()
            doc.close()
            return text.strip()
        except Exception as e:
            print(f"Error extracting text: {e}")
            return ""
    
    def create_chunks(self, text: str, filename: str) -> List[Dict]:
        """Create chunks using improved strategy"""
        if not text:
            return []
        
        # Strategy 1: Try heading-based chunking
        chunks = self._chunk_by_headings(text, filename)
        if len(chunks) > 1:
            return chunks
        
        # Strategy 2: Try medical pattern chunking
        chunks = self._chunk_by_medical_patterns(text, filename)
        if len(chunks) > 1:
            return chunks
        
        # Strategy 3: Fallback to smart word chunking
        return self._chunk_by_words_smart(text, filename)
    
    def _chunk_by_headings(self, text: str, filename: str) -> List[Dict]:
        """Chunk by markdown-style headings"""
        lines = text.split('\n')
        chunks = []
        current_chunk = []
        current_heading = filename.replace('.pdf', '')
        chunk_index = 0
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # Check for heading patterns
            if line.startswith('#') or self._is_heading_line(line):
                # Save previous chunk
                if current_chunk:
                    chunk_text = '\n'.join(current_chunk).strip()
                    if len(chunk_text.split()) > 5:  # Minimum word threshold
                        chunks.append(self._create_chunk(
                            chunk_text, filename, chunk_index, current_heading
                        ))
                        chunk_index += 1
                
                # Start new chunk
                current_heading = line.replace('#', '').strip()
                current_chunk = [line]
            else:
                current_chunk.append(line)
        
        # Add final chunk
        if current_chunk:
            chunk_text = '\n'.join(current_chunk).strip()
            if len(chunk_text.split()) > 5:
                chunks.append(self._create_chunk(
                    chunk_text, filename, chunk_index, current_heading
                ))
        
        return chunks
    
    def _chunk_by_medical_patterns(self, text: str, filename: str) -> List[Dict]:
        """Chunk by medical section patterns"""
        lines = text.split('\n')
        chunks = []
        current_chunk = []
        current_section = "Introduction"
        chunk_index = 0
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # Check if line is a medical section header
            if self._is_medical_section(line):
                # Save previous chunk
                if current_chunk:
                    chunk_text = '\n'.join(current_chunk).strip()
                    if len(chunk_text.split()) > 10:  # Minimum threshold
                        chunks.append(self._create_chunk(
                            chunk_text, filename, chunk_index, current_section
                        ))
                        chunk_index += 1
                
                # Start new section
                current_section = line.replace(':', '').strip()
                current_chunk = [line]
            else:
                current_chunk.append(line)
                
                # Check if chunk is getting too long
                if len(' '.join(current_chunk).split()) > self._get_section_max_size(current_section):
                    # Split at a good point
                    break_point = self._find_break_point(current_chunk)
                    if break_point > 0:
                        chunk_lines = current_chunk[:break_point]
                        remaining_lines = current_chunk[break_point:]
                        
                        chunk_text = '\n'.join(chunk_lines).strip()
                        chunks.append(self._create_chunk(
                            chunk_text, filename, chunk_index, f"{current_section} (Part {chunk_index + 1})"
                        ))
                        chunk_index += 1
                        
                        current_chunk = remaining_lines
        
        # Add final chunk
        if current_chunk:
            chunk_text = '\n'.join(current_chunk).strip()
            if len(chunk_text.split()) > 5:
                chunks.append(self._create_chunk(
                    chunk_text, filename, chunk_index, current_section
                ))
        
        return chunks
    
    def _chunk_by_words_smart(self, text: str, filename: str) -> List[Dict]:
        """Smart word-based chunking with sentence awareness"""
        sentences = re.split(r'[.!?]+', text)
        chunks = []
        current_chunk = []
        current_word_count = 0
        chunk_index = 0
        
        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue
            
            sentence_words = len(sentence.split())
            
            if current_word_count + sentence_words > self.base_chunk_size and current_chunk:
                # Create chunk
                chunk_text = '. '.join(current_chunk).strip()
                chunks.append(self._create_chunk(
                    chunk_text, filename, chunk_index, f"Section {chunk_index + 1}"
                ))
                chunk_index += 1
                
                # Start new chunk
                current_chunk = [sentence]
                current_word_count = sentence_words
            else:
                current_chunk.append(sentence)
                current_word_count += sentence_words
        
        # Add final chunk
        if current_chunk:
            chunk_text = '. '.join(current_chunk).strip()
            chunks.append(self._create_chunk(
                chunk_text, filename, chunk_index, f"Section {chunk_index + 1}"
            ))
        
        return chunks
    
    def _is_heading_line(self, line: str) -> bool:
        """Check if line looks like a heading"""
        # All caps and short
        if line.isupper() and 3 < len(line) < 50:
            return True
        
        # Numbered section
        if re.match(r'^\d+\.?\s+[A-Z]', line):
            return True
        
        # Title case and short
        if line.istitle() and 3 < len(line) < 50 and ':' not in line:
            return True
        
        return False
    
    def _is_medical_section(self, line: str) -> bool:
        """Check if line is a medical section header"""
        line_lower = line.lower().replace(':', '').strip()
        
        # Direct keyword match
        if line_lower in self.medical_keywords:
            return True
        
        # Partial keyword match
        for keyword in self.medical_keywords:
            if keyword in line_lower and len(line_lower) < 30:
                return True
        
        return False
    
    def _get_section_max_size(self, section_name: str) -> int:
        """Get max size for different section types"""
        section_lower = section_name.lower()
        
        if any(word in section_lower for word in ['symptom', 'sign']):
            return 1000  # Symptoms can be detailed
        elif any(word in section_lower for word in ['treatment', 'therapy']):
            return 1200  # Treatment can be complex
        elif any(word in section_lower for word in ['overview', 'introduction']):
            return 600   # Keep introductions concise
        else:
            return 800   # Default
    
    def _find_break_point(self, lines: List[str]) -> int:
        """Find good break point in chunk"""
        if len(lines) < 3:
            return len(lines)
        
        # Look for sentence endings
        for i in range(len(lines) // 2, len(lines) - 1):
            if lines[i].strip().endswith('.'):
                return i + 1
        
        return len(lines) // 2
    
    def _create_chunk(self, text: str, filename: str, chunk_index: int, heading: str) -> Dict:
        """Create chunk dictionary"""
        chunk_id = hashlib.md5(f"{filename}_{chunk_index}_{heading}_{text[:50]}".encode()).hexdigest()
        
        return {
            "chunk_id": chunk_id,
            "filename": filename,
            "chunk_index": chunk_index,
            "text": text,
            "heading": heading,
            "word_count": len(text.split())
        }

def test_new_chunking():
    """Test the new chunking strategy"""
    
    print("🧪 Testing New Chunking Strategy")
    print("=" * 50)
    
    chunker = MedicalDocumentChunker()
    
    # Test with sample medical text
    sample_text = """
ACIDOSIS

SYMPTOMS
- Decreased appetite
- Lethargy and weakness
- Rapid breathing
- Dehydration signs

TREATMENT
Fluid therapy is essential. Administer sodium bicarbonate to correct pH.
Monitor electrolyte levels carefully. Provide supportive care as needed.

PREVENTION
Maintain proper nutrition. Regular health monitoring is important.
Ensure adequate water supply.
"""
    
    chunks = chunker.create_chunks(sample_text, "test.pdf")
    
    print(f"Created {len(chunks)} chunks:")
    for i, chunk in enumerate(chunks, 1):
        print(f"\nChunk {i}: {chunk['heading']}")
        print(f"Words: {chunk['word_count']}")
        print(f"Content: {chunk['text'][:100]}...")
    
    # Test with real PDF if available
    pdf_path = "diseases/ACIDOSIS.pdf"
    try:
        text = chunker.extract_text_from_pdf(pdf_path)
        if text:
            print(f"\n📄 Testing with real PDF: {pdf_path}")
            chunks = chunker.create_chunks(text, "ACIDOSIS.pdf")
            print(f"Created {len(chunks)} chunks from real PDF")
            
            for i, chunk in enumerate(chunks[:3], 1):  # Show first 3
                print(f"\nReal Chunk {i}: {chunk['heading']} ({chunk['word_count']} words)")
    except:
        print("📄 Real PDF test skipped (file not found)")

if __name__ == "__main__":
    test_new_chunking()
