"""
Search System using LlamaIndex with PostgreSQL Vector Store and Grok LLM
"""

import os
import requests
import json
from typing import List, Dict, Optional
from dotenv import load_dotenv
import psycopg2
from pdf_embedder import NomicEmbedder, PostgreSQLManager

# Load environment variables
load_dotenv()

class GroqLLM:
    """Groq LLM integration for final response generation"""

    def __init__(self, api_key: str = None):
        self.api_key = api_key or os.getenv("GROQ_API_KEY")
        self.base_url = "https://api.groq.com/openai/v1"
        self.model = "llama-3.3-70b-versatile"  # Using Groq's fast Llama model

    def generate_response(self, query: str, context: str) -> str:
        """Generate response using Groq LLM"""

        prompt = f"""You are an expert medical assistant specializing in animal diseases and veterinary medicine.
Use the provided context to answer the user's question accurately and comprehensively.

Context from medical documents:
{context}

User Question: {query}

Instructions:
- Provide a detailed, accurate answer based solely on the context provided
- If the context doesn't contain enough information, clearly state what information is missing
- Include relevant symptoms, treatments, or diagnostic information when available
- Use medical terminology appropriately but explain complex terms
- Do not recommend treatments without proper veterinary consultation disclaimers

Answer:"""

        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        payload = {
            "messages": [
                {
                    "role": "system",
                    "content": "You are an expert veterinary medical assistant. Provide accurate, helpful information about animal diseases and health conditions based on the provided context."
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "model": self.model,
            "stream": False,
            "temperature": 0.3
        }

        try:
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=payload,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                return result["choices"][0]["message"]["content"]
            else:
                print(f"Groq API Error: {response.status_code}")
                print(f"Response: {response.text}")
                return f"Error generating response: {response.status_code}"

        except Exception as e:
            print(f"Error calling Groq API: {e}")
            return f"Error generating response: {str(e)}"

class VectorSearchEngine:
    """Vector search engine using PostgreSQL and embeddings"""
    
    def __init__(self):
        self.embedder = NomicEmbedder()
        self.db_manager = PostgreSQLManager()
        
    def search_similar_documents(self, query: str, top_k: int = 10) -> List[Dict]:
        """Search for similar documents using vector similarity"""
        
        # Get query embedding
        query_embeddings = self.embedder.get_embeddings([query])
        if not query_embeddings:
            return []
            
        query_embedding = query_embeddings[0]
        
        # Search in database
        search_sql = """
        SELECT
            chunk_id,
            filename,
            content,
            heading,
            word_count,
            1 - (embedding <=> %s::vector) as similarity_score
        FROM documents
        ORDER BY embedding <=> %s::vector
        LIMIT %s
        """
        
        try:
            with self.db_manager.get_connection() as conn:
                with conn.cursor() as cur:
                    cur.execute(search_sql, (query_embedding, query_embedding, top_k))
                    results = cur.fetchall()
                    
                    # Format results
                    documents = []
                    for row in results:
                        documents.append({
                            "chunk_id": row[0],
                            "filename": row[1],
                            "content": row[2],
                            "heading": row[3],
                            "word_count": row[4],
                            "similarity_score": float(row[5])
                        })
                    
                    return documents
                    
        except Exception as e:
            print(f"Error searching documents: {e}")
            return []
    
    def search_by_filename(self, filename_pattern: str, top_k: int = 5) -> List[Dict]:
        """Search documents by filename pattern"""
        
        search_sql = """
        SELECT 
            chunk_id,
            filename,
            content,
            word_count
        FROM documents 
        WHERE filename ILIKE %s
        ORDER BY chunk_index
        LIMIT %s
        """
        
        try:
            with self.db_manager.get_connection() as conn:
                with conn.cursor() as cur:
                    cur.execute(search_sql, (f"%{filename_pattern}%", top_k))
                    results = cur.fetchall()
                    
                    documents = []
                    for row in results:
                        documents.append({
                            "chunk_id": row[0],
                            "filename": row[1],
                            "content": row[2],
                            "word_count": row[3],
                            "similarity_score": 1.0  # Perfect match for filename search
                        })
                    
                    return documents
                    
        except Exception as e:
            print(f"Error searching by filename: {e}")
            return []

class MedicalSearchSystem:
    """Complete medical search system combining vector search and LLM"""
    
    def __init__(self):
        self.search_engine = VectorSearchEngine()
        self.llm = GroqLLM()
        
    def search_and_answer(self, query: str, top_k: int = 10) -> Dict:
        """Search for relevant documents and generate answer"""
        
        print(f"Searching for: {query}")
        print("=" * 50)
        
        # Search for similar documents
        documents = self.search_engine.search_similar_documents(query, top_k)
        
        if not documents:
            return {
                "query": query,
                "documents_found": 0,
                "answer": "No relevant documents found for your query.",
                "sources": []
            }
        
        # Prepare context from retrieved documents
        context_parts = []
        sources = []
        
        for i, doc in enumerate(documents, 1):
            heading = doc.get('heading', 'Unknown Section')
            context_parts.append(f"Document {i} ({doc['filename']} - {heading}):\n{doc['content']}")
            sources.append({
                "filename": doc["filename"],
                "heading": heading,
                "similarity_score": doc["similarity_score"],
                "chunk_id": doc["chunk_id"]
            })
        
        context = "\n\n---\n\n".join(context_parts)
        
        # Generate answer using Groq LLM
        print("Generating response with Groq LLM...")
        answer = self.llm.generate_response(query, context)
        
        return {
            "query": query,
            "documents_found": len(documents),
            "answer": answer,
            "sources": sources,
            "context": context
        }
    
    def get_database_stats(self) -> Dict:
        """Get database statistics"""
        try:
            with self.search_engine.db_manager.get_connection() as conn:
                with conn.cursor() as cur:
                    # Get total documents
                    cur.execute("SELECT COUNT(*) FROM documents")
                    total_docs = cur.fetchone()[0]
                    
                    # Get unique files
                    cur.execute("SELECT COUNT(DISTINCT filename) FROM documents")
                    unique_files = cur.fetchone()[0]
                    
                    # Get file list
                    cur.execute("SELECT filename, COUNT(*) as chunks FROM documents GROUP BY filename ORDER BY filename")
                    files = cur.fetchall()
                    
                    return {
                        "total_documents": total_docs,
                        "unique_files": unique_files,
                        "files": [{"filename": f[0], "chunks": f[1]} for f in files]
                    }
                    
        except Exception as e:
            print(f"Error getting database stats: {e}")
            return {"error": str(e)}

def main():
    """Main interactive search interface"""
    
    search_system = MedicalSearchSystem()
    
    print("🏥 Medical Document Search System")
    print("=" * 50)
    
    # Show database stats
    stats = search_system.get_database_stats()
    if "error" not in stats:
        print(f"📊 Database contains {stats['total_documents']} document chunks from {stats['unique_files']} files")
        print()
    
    while True:
        try:
            # Get user query
            query = input("\n🔍 Enter your medical question (or 'quit' to exit): ").strip()
            
            if query.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break
                
            if not query:
                continue
                
            # Search and get answer
            result = search_system.search_and_answer(query)
            
            # Display results
            print(f"\n📋 Found {result['documents_found']} relevant documents")
            print("\n🤖 Answer:")
            print("-" * 30)
            print(result['answer'])
            
            # Show sources
            if result['sources']:
                print(f"\n📚 Sources:")
                for i, source in enumerate(result['sources'][:5], 1):
                    print(f"{i}. {source['filename']} (similarity: {source['similarity_score']:.3f})")
            
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
