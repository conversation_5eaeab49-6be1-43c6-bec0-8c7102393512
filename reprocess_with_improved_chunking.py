#!/usr/bin/env python3
"""
Reprocess documents with improved chunking strategy
"""

import os
import psycopg2
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def clear_database():
    """Clear existing documents to reprocess with new chunking"""
    
    print("🗑️  Clearing existing documents...")
    
    try:
        conn = psycopg2.connect(
            dbname=os.getenv("DB_NAME", "kforce"),
            user=os.getenv("DB_USER", "postgres"),
            password=os.getenv("DB_PASS", "kforce"),
            host=os.getenv("DB_HOST", "localhost"),
            port=os.getenv("DB_PORT", "5444")
        )
        
        with conn.cursor() as cur:
            # Get current count
            cur.execute("SELECT COUNT(*) FROM documents")
            old_count = cur.fetchone()[0]
            print(f"📊 Current documents: {old_count}")
            
            # Clear the table
            cur.execute("DELETE FROM documents")
            conn.commit()
            
            print("✅ Database cleared successfully!")
            
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error clearing database: {e}")
        return False

def reprocess_documents():
    """Reprocess documents with improved chunking"""
    
    print("🔄 Reprocessing documents with improved chunking...")
    
    try:
        # Import here to avoid any import issues
        from pdf_embedder import PDFEmbeddingPipeline
        
        # Create pipeline
        pipeline = PDFEmbeddingPipeline()
        
        # Get training folder
        training_folder = os.getenv("TRAINING_DATA_PATH", "diseases")
        
        # Process the folder
        pipeline.process_pdf_folder(training_folder)
        
        print("✅ Reprocessing completed!")
        return True
        
    except Exception as e:
        print(f"❌ Error reprocessing: {e}")
        import traceback
        traceback.print_exc()
        return False

def compare_results():
    """Compare the new chunking results"""
    
    print("📊 Analyzing new chunking results...")
    
    try:
        conn = psycopg2.connect(
            dbname=os.getenv("DB_NAME", "kforce"),
            user=os.getenv("DB_USER", "postgres"),
            password=os.getenv("DB_PASS", "kforce"),
            host=os.getenv("DB_HOST", "localhost"),
            port=os.getenv("DB_PORT", "5444")
        )
        
        with conn.cursor() as cur:
            # Get total stats
            cur.execute("SELECT COUNT(*) FROM documents")
            total_docs = cur.fetchone()[0]
            
            cur.execute("SELECT COUNT(DISTINCT filename) FROM documents")
            unique_files = cur.fetchone()[0]
            
            cur.execute("SELECT AVG(word_count) FROM documents")
            avg_words = cur.fetchone()[0]
            
            print(f"📈 New Results:")
            print(f"   Total chunks: {total_docs}")
            print(f"   Unique files: {unique_files}")
            print(f"   Average chunk size: {avg_words:.1f} words")
            
            # Show sample headings
            cur.execute("""
                SELECT heading, COUNT(*) as count 
                FROM documents 
                GROUP BY heading 
                ORDER BY count DESC 
                LIMIT 10
            """)
            
            headings = cur.fetchall()
            print(f"\n📋 Most common headings:")
            for heading, count in headings:
                print(f"   • {heading}: {count} chunks")
            
            # Show sample from a specific file
            cur.execute("""
                SELECT heading, word_count, content 
                FROM documents 
                WHERE filename = 'ACIDOSIS.pdf' 
                ORDER BY chunk_index 
                LIMIT 5
            """)
            
            acidosis_chunks = cur.fetchall()
            print(f"\n📄 Sample from ACIDOSIS.pdf:")
            for heading, word_count, content in acidosis_chunks:
                print(f"   • {heading} ({word_count} words)")
                print(f"     {content[:100]}...")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error analyzing results: {e}")

def main():
    """Main function"""
    
    print("🚀 Improved Chunking Strategy Implementation")
    print("=" * 60)
    
    print("\n💡 Improvements in new chunking strategy:")
    print("   • Enhanced medical section detection")
    print("   • Adaptive chunk sizes based on content type")
    print("   • Better handling of all-caps headers")
    print("   • Smarter break points at sentence boundaries")
    print("   • Minimum content thresholds")
    print("   • Support for numbered sections")
    
    # Ask user if they want to proceed
    proceed = input("\n🔄 Reprocess all documents with improved chunking? (y/N): ").strip().lower()
    
    if proceed in ['y', 'yes']:
        # Clear existing data
        if clear_database():
            # Reprocess with new chunking
            if reprocess_documents():
                # Show results
                compare_results()
                
                print("\n✅ Improved chunking implementation complete!")
                print("\n🎯 Key benefits:")
                print("   • Better semantic organization")
                print("   • More accurate search results")
                print("   • Preserved document structure")
                print("   • Adaptive content sizing")
            else:
                print("\n❌ Reprocessing failed!")
        else:
            print("\n❌ Database clearing failed!")
    else:
        print("\n⏭️  Skipping reprocessing. Current data preserved.")
        compare_results()

if __name__ == "__main__":
    main()
