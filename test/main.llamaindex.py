import os
from datetime import datetime
from llama_index.vector_stores.postgres import PGVectorStore
from llama_index.core import VectorStoreIndex, StorageContext
from llama_index.core.query_engine import RetrieverQueryEngine
from llama_index.core.retrievers import VectorIndexRetriever
from llama_index.core.response_synthesizers import ResponseMode
from llama_index.llms.ollama import Ollama
from llama_index.core import Settings
from transformers import AutoTokenizer, AutoModel
from llama_index.core.embeddings import BaseEmbedding
from ollama import chat
import torch

# --- Custom Nomic Embedder  ---
class NomicEmbedding(BaseEmbedding):
    def __init__(self, model_name="nomic-ai/nomic-embed-text-v1", **kwargs):
        super().__init__(**kwargs)
        # Use object.__setattr__ to bypass Pydantic validation
        object.__setattr__(self, 'tokenizer', AutoTokenizer.from_pretrained(model_name, trust_remote_code=True))
        object.__setattr__(self, 'model', AutoModel.from_pretrained(model_name, trust_remote_code=True))
        self.model.eval()

    class Config:
        arbitrary_types_allowed = True

    def _get_embedding(self, text: str):
        with torch.no_grad():
            inputs = self.tokenizer(text, padding=True, truncation=True, return_tensors="pt")
            outputs = self.model(**inputs)
            return outputs.last_hidden_state[:, 0, :].squeeze(0).cpu().tolist()

    def _get_text_embedding(self, text: str):
        """Get embedding for a single text"""
        return self._get_embedding(text)

    def _get_text_embeddings(self, texts):
        """Get embeddings for multiple texts"""
        return [self._get_embedding(text) for text in texts]

    def _get_query_embedding(self, query: str):
        """Get embedding for a query"""
        return self._get_embedding(query)

    async def _aget_query_embedding(self, query: str):
        """Async version of get_query_embedding"""
        return self._get_query_embedding(query)

# --- CONFIG (Same as your embedding code) ---
DB_NAME = "kforce"
DB_USER = "postgres"
DB_PASS = "kforce"
DB_HOST = "localhost"
DB_PORT = "5444"
DB_TABLE = "documents_improved"  # Using improved indexing table

# --- RAG Configuration ---
TOP_K = 5  # Number of documents to retrieve
OLLAMA_MODEL = "llama3.2"  # Change this to your preferred Ollama model
OLLAMA_BASE_URL = "http://localhost:11434"  # Default Ollama URL

class RAGSystem:
    def __init__(self):
        """Initialize the RAG system"""
        print("🔧 Initializing RAG system...")
        
        # Initialize embedding model (same as used for indexing)
        self.embed_model = NomicEmbedding()
        
        # Connect to existing vector store
        self.vector_store = PGVectorStore.from_params(
            database=DB_NAME,
            user=DB_USER,
            password=DB_PASS,
            host=DB_HOST,
            port=int(DB_PORT),
            table_name=DB_TABLE,
            embed_dim=768
        )
        
        # Create storage context
        self.storage_context = StorageContext.from_defaults(vector_store=self.vector_store)
        
        # Load index from existing vector store
        self.index = VectorStoreIndex.from_vector_store(
            self.vector_store,
            embed_model=self.embed_model
        )
        
        # Initialize LLM (Ollama)
        self.llm = Ollama(
            model=OLLAMA_MODEL,
            base_url=OLLAMA_BASE_URL,
            request_timeout=120.0
        )
        
        # Set global settings
        Settings.embed_model = self.embed_model
        Settings.llm = self.llm
        
        # Create query engine
        self.query_engine = self.index.as_query_engine(
            similarity_top_k=TOP_K,
            response_mode=ResponseMode.COMPACT
        )
        
        print("✅ RAG system initialized successfully!")

    def retrieve_only(self, question: str, top_k: int = TOP_K):
        """
        Only retrieve relevant documents without generating answer

        Args:
            question (str): The question to search for
            top_k (int): Number of documents to retrieve

        Returns:
            list: Retrieved documents with metadata
        """
        print(f"\n🔍 Retrieving top {top_k} documents for: {question}")

        try:
            # Create retriever
            retriever = VectorIndexRetriever(
                index=self.index,
                similarity_top_k=top_k
            )

            # Retrieve documents
            retrieved_nodes = retriever.retrieve(question)

            # Print all document IDs
            print(f"\n📋 Retrieved Document IDs:")
            for i, node in enumerate(retrieved_nodes, 1):
                # Try to get various possible ID fields
                doc_id = getattr(node, 'node_id', None) or getattr(node, 'id_', None) or getattr(node, 'doc_id', None)
                if doc_id:
                    print(f"   {i}. Document ID: {doc_id}")
                else:
                    print(f"   {i}. Document ID: Not available")

            results = []
            for i, node in enumerate(retrieved_nodes, 1):
                # Extract document ID
                doc_id = getattr(node, 'node_id', None) or getattr(node, 'id_', None) or getattr(node, 'doc_id', None)

                result = {
                    'rank': i,
                    'document_id': doc_id,
                    'score': getattr(node, 'score', 0.0),
                    'source': node.metadata.get('source', 'Unknown'),
                    'total_pages': node.metadata.get('total_pages', 'Unknown'),
                    'text_preview': node.text[:200] + "..." if len(node.text) > 200 else node.text,
                    'text': node.text  # Full text for context
                }
                results.append(result)



            return results

        except Exception as e:
            print(f"❌ Error during retrieval: {str(e)}")
            return []
    
    def chat_with_context(self, question: str, context_docs: list, verbose: bool = True):
        """
        Use Ollama chat directly with retrieved context

        Args:
            question (str): The question to ask
            context_docs (list): Retrieved documents for context
            verbose (bool): Whether to show detailed information

        Returns:
            dict: Response with answer and metadata
        """
        if verbose:
            print(f"\n❓ Question: {question}")
            print("🔍 Using Ollama chat with retrieved context...")

        try:
            # Prepare context from retrieved documents
            context_text = ""
            sources = []

            for i, doc in enumerate(context_docs):
                context_text += f"\n--- Document {i+1}: {doc.get('source', 'Unknown')} ---\n"
                context_text += doc.get('text', doc.get('text_preview', ''))[:1500] + "\n"

                sources.append({
                    'source': doc.get('source', 'Unknown'),
                    'score': doc.get('score', 0.0),
                    'total_pages': doc.get('total_pages', 'Unknown')
                })
            
            # Create prompt with context
            prompt = f"""Based on the following context documents, please answer the question.Don't recommend any other information other than the context provided.

Context:
{context_text}

Question: {question}

Please provide a comprehensive answer based on the context provided. If the context doesn't contain enough information to answer the question, please say so.

Answer:"""
            
            # Use Ollama chat
            response = chat(
                model=OLLAMA_MODEL,
                messages=[
                    {
                        'role': 'user',
                        'content': prompt
                    }
                ]
            )
            
            answer = response['message']['content']
            
            result = {
                'answer': answer,
                'sources': sources,
                'timestamp': datetime.now().isoformat(),
                'model_used': OLLAMA_MODEL
            }
            
            if verbose:
                print(f"\n💡 Answer: {result['answer']}")
                print(f"\n📚 Sources ({len(sources)} documents):")
                
            
            return result
            
        except Exception as e:
            error_msg = f"Error during Ollama chat: {str(e)}"
            print(f"❌ {error_msg}")
            return {
                'answer': error_msg,
                'sources': [],
                'timestamp': datetime.now().isoformat()
            }

    def query(self, question: str, verbose: bool = True, use_ollama_direct: bool = True):
        """
        Query the RAG system
        
        Args:
            question (str): The question to ask
            verbose (bool): Whether to show detailed information
            use_ollama_direct (bool): Whether to use Ollama chat directly or LlamaIndex
            
        Returns:
            dict: Response with answer and metadata
        """
        if use_ollama_direct:
            # First retrieve relevant documents
            retrieved_docs = self.retrieve_only(question, top_k=TOP_K)
            if not retrieved_docs:
                return {
                    'answer': "No relevant documents found for your question.",
                    'sources': [],
                    'timestamp': datetime.now().isoformat()
                }
            
            # Use Ollama chat with context
            return self.chat_with_context(question, retrieved_docs, verbose)
        
        else:
            # Use LlamaIndex query engine (original method)
            if verbose:
                print(f"\n❓ Question: {question}")
                print("🔍 Searching relevant documents...")
            
            try:
                # Get response from query engine
                response = self.query_engine.query(question)
                
                # Extract source information
                sources = []
                if hasattr(response, 'source_nodes'):
                    for node in response.source_nodes:
                        if hasattr(node, 'metadata'):
                            sources.append({
                                'source': node.metadata.get('source', 'Unknown'),
                                'score': getattr(node, 'score', 0.0),
                                'total_pages': node.metadata.get('total_pages', 'Unknown')
                            })
                
                result = {
                    'answer': str(response),
                    'sources': sources,
                    'timestamp': datetime.now().isoformat()
                }
                
                if verbose:
                    print(f"\n💡 Answer: {result['answer']}")
                    print(f"\n📚 Sources ({len(sources)} documents):")
                    for i, source in enumerate(sources, 1):
                        print(f"   {i}. {source['source']} (Score: {source['score']:.3f}, Pages: {source['total_pages']})")
                
                return result
                
            except Exception as e:
                error_msg = f"Error during query: {str(e)}"
                print(f"❌ {error_msg}")
                return {
                    'answer': error_msg,
                    'sources': [],
                    'timestamp': datetime.now().isoformat()
                }

def main():
    """Main function to run the RAG system"""
    print("🚀 Starting RAG Query System with Ollama")
    print("=" * 50)
    
    try:
        # Initialize RAG system
        rag = RAGSystem()
        
        # Test Ollama connection
        print("🔍 Testing Ollama connection...")
        try:
            test_response = chat(
                model=OLLAMA_MODEL,
                messages=[{'role': 'user', 'content': 'Hello, are you working?'}]
            )
            print(f"✅ Ollama connection successful! Using model: {OLLAMA_MODEL}")
        except Exception as e:
            print(f"❌ Ollama connection failed: {str(e)}")
            print("💡 Make sure Ollama is running and the model is available")
            print(f"💡 Try: ollama pull {OLLAMA_MODEL}")
            return
        
        # Interactive query loop
        print("\n🎯 RAG system ready! Type your questions below.")
        print("Commands:")
        print("  - 'quit' or 'exit' to stop")
        print("  - 'retrieve <question>' to only retrieve documents")
        print("  - 'llamaindex <question>' to use LlamaIndex query engine")
        print("  - Just type your question for Ollama RAG response")
        print("-" * 50)
        
        while True:
            try:
                user_input = input("\n❓ Your question: ").strip()
                
                if not user_input:
                    continue
                    
                if user_input.lower() in ['quit', 'exit']:
                    print("👋 Goodbye!")
                    break
                
                if user_input.lower().startswith('retrieve '):
                    # Retrieve only mode
                    question = user_input[9:].strip()
                    if question:
                        rag.retrieve_only(question)
                
                elif user_input.lower().startswith('llamaindex '):
                    # LlamaIndex query engine mode
                    question = user_input[11:].strip()
                    if question:
                        result = rag.query(question, use_ollama_direct=False)
                        
                else:
                    # Ollama RAG query (default)
                    result = rag.query(user_input, use_ollama_direct=True)
                    
            except KeyboardInterrupt:
                print("\n\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {str(e)}")
                
    except Exception as e:
        print(f"❌ Failed to initialize RAG system: {str(e)}")
        print("💡 Make sure your database is running and contains indexed documents")

# --- Example usage ---
if __name__ == "__main__":
    main()

# --- Alternative: Direct usage example ---
def example_usage():
    """Example of how to use the RAG system programmatically"""
    
    # Initialize RAG system
    rag = RAGSystem()
    
    # Example queries using Ollama
    questions = [
        "What are the symptoms of diabetes?",
        "How is hypertension treated?",
        "What causes heart disease?"
    ]
    
    for question in questions:
        print(f"\n{'='*80}")
        print("🔥 Using Ollama RAG:")
        result = rag.query(question, use_ollama_direct=True, verbose=True)
        
        print(f"\n{'='*80}")
        print("📚 Using LlamaIndex:")
        result = rag.query(question, use_ollama_direct=False, verbose=True)
        print("\n" + "="*80 + "\n")
    
    # Example of retrieval only
    print("🔍 Retrieval only example:")
    rag.retrieve_only("diabetes complications", top_k=2)