#!/usr/bin/env python3
"""
Analyze PDFs in the pdf folder to understand their structure and improve chunking
"""

import fitz
import random
import re
from pathlib import Path
from collections import Counter

def analyze_pdf_sample(pdf_path):
    """Analyze a single PDF to understand its structure"""
    try:
        doc = fitz.open(pdf_path)
        text = ""
        for page in doc:
            text += page.get_text()
        doc.close()
        
        lines = [line.strip() for line in text.split('\n') if line.strip()]
        
        analysis = {
            'filename': pdf_path.name,
            'total_lines': len(lines),
            'total_words': len(' '.join(lines).split()),
            'first_10_lines': lines[:10],
            'structure_patterns': [],
            'content_type': 'unknown'
        }
        
        # Analyze content patterns
        full_text = ' '.join(lines).lower()
        
        # Check for different document types
        if any(word in full_text for word in ['invoice', 'bill', 'amount', 'total', 'payment']):
            analysis['content_type'] = 'financial'
        elif any(word in full_text for word in ['contract', 'agreement', 'terms', 'conditions']):
            analysis['content_type'] = 'legal'
        elif any(word in full_text for word in ['report', 'analysis', 'summary', 'findings']):
            analysis['content_type'] = 'report'
        elif any(word in full_text for word in ['name', 'address', 'phone', 'email']):
            analysis['content_type'] = 'personal'
        
        # Look for structural patterns
        for i, line in enumerate(lines[:20]):  # Check first 20 lines
            # Headers (all caps, short)
            if line.isupper() and 3 < len(line) < 50:
                analysis['structure_patterns'].append(('HEADER', line))
            
            # Dates
            if re.search(r'\d{1,2}[/-]\d{1,2}[/-]\d{2,4}', line):
                analysis['structure_patterns'].append(('DATE', line))
            
            # Numbers/amounts
            if re.search(r'[\$₹]\s*\d+|amount|total', line.lower()):
                analysis['structure_patterns'].append(('AMOUNT', line))
            
            # Names (title case with common name patterns)
            if re.search(r'^[A-Z][a-z]+\s+[A-Z][a-z]+', line):
                analysis['structure_patterns'].append(('NAME', line))
        
        return analysis
        
    except Exception as e:
        return {
            'filename': pdf_path.name,
            'error': str(e),
            'total_lines': 0,
            'total_words': 0
        }

def analyze_pdf_folder():
    """Analyze sample PDFs from the pdf folder"""
    
    print("🔍 Analyzing PDF Folder Structure")
    print("=" * 60)
    
    pdf_folder = Path('pdf')
    all_pdfs = list(pdf_folder.glob('*.pdf'))
    
    print(f"📁 Found {len(all_pdfs)} PDF files")
    
    # Sample 10 random PDFs for analysis
    sample_size = min(10, len(all_pdfs))
    sample_pdfs = random.sample(all_pdfs, sample_size)
    
    print(f"📊 Analyzing {sample_size} sample PDFs:")
    
    analyses = []
    content_types = []
    all_patterns = []
    word_counts = []
    
    for i, pdf_path in enumerate(sample_pdfs, 1):
        print(f"\n{i}. Analyzing {pdf_path.name}...")
        
        analysis = analyze_pdf_sample(pdf_path)
        analyses.append(analysis)
        
        if 'error' not in analysis:
            content_types.append(analysis['content_type'])
            word_counts.append(analysis['total_words'])
            all_patterns.extend([p[0] for p in analysis['structure_patterns']])
            
            print(f"   📄 Lines: {analysis['total_lines']}")
            print(f"   📝 Words: {analysis['total_words']}")
            print(f"   📋 Type: {analysis['content_type']}")
            print(f"   🔍 Patterns: {len(analysis['structure_patterns'])}")
            
            # Show first few lines
            print("   📖 First 5 lines:")
            for j, line in enumerate(analysis['first_10_lines'][:5], 1):
                print(f"      {j}. {line}")
        else:
            print(f"   ❌ Error: {analysis['error']}")
    
    # Summary analysis
    print(f"\n📊 SUMMARY ANALYSIS")
    print("=" * 40)
    
    if content_types:
        type_counts = Counter(content_types)
        print(f"📋 Content Types:")
        for content_type, count in type_counts.most_common():
            print(f"   • {content_type}: {count} documents")
    
    if all_patterns:
        pattern_counts = Counter(all_patterns)
        print(f"\n🔍 Common Patterns:")
        for pattern, count in pattern_counts.most_common():
            print(f"   • {pattern}: {count} occurrences")
    
    if word_counts:
        avg_words = sum(word_counts) / len(word_counts)
        min_words = min(word_counts)
        max_words = max(word_counts)
        print(f"\n📈 Word Count Statistics:")
        print(f"   • Average: {avg_words:.0f} words")
        print(f"   • Range: {min_words} - {max_words} words")
    
    return analyses

def recommend_chunking_strategy(analyses):
    """Recommend chunking strategy based on analysis"""
    
    print(f"\n🧠 RECOMMENDED CHUNKING STRATEGY")
    print("=" * 50)
    
    # Analyze document characteristics
    content_types = [a.get('content_type', 'unknown') for a in analyses if 'error' not in a]
    word_counts = [a.get('total_words', 0) for a in analyses if 'error' not in a]
    
    if not word_counts:
        print("❌ No valid documents to analyze")
        return
    
    avg_words = sum(word_counts) / len(word_counts)
    
    print(f"📊 Document Analysis:")
    print(f"   • Average document size: {avg_words:.0f} words")
    print(f"   • Document types: {set(content_types)}")
    
    # Recommend strategy based on document size and type
    if avg_words < 500:
        strategy = "single_document"
        chunk_size = 0  # Don't split small documents
        print(f"\n💡 Recommended Strategy: SINGLE DOCUMENT")
        print(f"   • Documents are small ({avg_words:.0f} words average)")
        print(f"   • Keep each document as one chunk")
        print(f"   • Preserve complete document context")
        
    elif avg_words < 2000:
        strategy = "moderate_chunking"
        chunk_size = 800
        print(f"\n💡 Recommended Strategy: MODERATE CHUNKING")
        print(f"   • Documents are medium-sized ({avg_words:.0f} words average)")
        print(f"   • Use {chunk_size}-word chunks with smart breaks")
        print(f"   • Preserve document structure where possible")
        
    else:
        strategy = "aggressive_chunking"
        chunk_size = 600
        print(f"\n💡 Recommended Strategy: AGGRESSIVE CHUNKING")
        print(f"   • Documents are large ({avg_words:.0f} words average)")
        print(f"   • Use {chunk_size}-word chunks with overlap")
        print(f"   • Focus on semantic coherence")
    
    # Content-specific recommendations
    if 'financial' in content_types:
        print(f"\n💰 Financial Document Handling:")
        print(f"   • Preserve amount and date information")
        print(f"   • Keep transaction details together")
        print(f"   • Maintain invoice/bill structure")
    
    if 'legal' in content_types:
        print(f"\n⚖️ Legal Document Handling:")
        print(f"   • Preserve clause structure")
        print(f"   • Keep terms and conditions together")
        print(f"   • Maintain legal context")
    
    if 'personal' in content_types:
        print(f"\n👤 Personal Document Handling:")
        print(f"   • Keep contact information together")
        print(f"   • Preserve personal details context")
        print(f"   • Maintain document integrity")
    
    return {
        'strategy': strategy,
        'chunk_size': chunk_size,
        'avg_words': avg_words,
        'content_types': content_types
    }

def main():
    """Main analysis function"""
    
    # Analyze the PDF folder
    analyses = analyze_pdf_folder()
    
    # Get chunking recommendations
    recommendations = recommend_chunking_strategy(analyses)
    
    print(f"\n🚀 NEXT STEPS")
    print("=" * 30)
    print(f"1. Update chunking strategy based on recommendations")
    print(f"2. Clear existing database: DELETE FROM documents")
    print(f"3. Process PDFs: python main_app.py --setup")
    print(f"4. Test search functionality")
    
    print(f"\n📝 Implementation Notes:")
    print(f"   • Total documents to process: ~13,097 PDFs")
    print(f"   • Estimated processing time: 2-3 hours")
    print(f"   • Database storage: ~50-100MB")
    print(f"   • Recommended chunk strategy: {recommendations.get('strategy', 'unknown')}")

if __name__ == "__main__":
    main()
