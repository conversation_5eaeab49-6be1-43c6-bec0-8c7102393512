#!/usr/bin/env python3
"""
Clear database and reprocess PDFs with improved chunking strategy
"""

import os
import psycopg2
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def clear_database():
    """Clear existing documents from database"""
    
    print("🗑️  Clearing existing documents from database...")
    
    try:
        conn = psycopg2.connect(
            dbname=os.getenv("DB_NAME", "kforce"),
            user=os.getenv("DB_USER", "postgres"),
            password=os.getenv("DB_PASS", "kforce"),
            host=os.getenv("DB_HOST", "localhost"),
            port=os.getenv("DB_PORT", "5444")
        )
        
        with conn.cursor() as cur:
            # Get current count
            cur.execute("SELECT COUNT(*) FROM documents")
            old_count = cur.fetchone()[0]
            print(f"📊 Current documents in database: {old_count}")
            
            if old_count > 0:
                # Clear the table
                cur.execute("DELETE FROM documents")
                conn.commit()
                print("✅ Database cleared successfully!")
            else:
                print("📝 Database is already empty")
            
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error clearing database: {e}")
        return False

def show_processing_info():
    """Show information about what will be processed"""
    
    print("📁 Processing Information:")
    print("=" * 40)
    
    training_path = os.getenv("TRAINING_DATA_PATH", "diseases")
    print(f"📂 Training data path: {training_path}")
    
    # Count PDFs
    from pathlib import Path
    pdf_folder = Path(training_path)
    
    if pdf_folder.exists():
        pdf_files = list(pdf_folder.glob("*.pdf"))
        print(f"📄 Total PDF files: {len(pdf_files)}")
        
        if len(pdf_files) > 0:
            print(f"📋 Sample files:")
            for i, pdf_file in enumerate(pdf_files[:5], 1):
                print(f"   {i}. {pdf_file.name}")
            if len(pdf_files) > 5:
                print(f"   ... and {len(pdf_files) - 5} more files")
        
        # Estimate processing time
        if len(pdf_files) > 1000:
            print(f"⏱️  Estimated processing time: 1-2 hours")
        elif len(pdf_files) > 100:
            print(f"⏱️  Estimated processing time: 10-30 minutes")
        else:
            print(f"⏱️  Estimated processing time: 1-5 minutes")
            
        return len(pdf_files)
    else:
        print(f"❌ Training data folder '{training_path}' not found!")
        return 0

def main():
    """Main function"""
    
    print("🚀 Clear Database and Reprocess with Improved Chunking")
    print("=" * 70)
    
    # Show what will be processed
    pdf_count = show_processing_info()
    
    if pdf_count == 0:
        print("❌ No PDFs found to process. Exiting.")
        return
    
    print(f"\n💡 Improved Chunking Features:")
    print(f"   • Smart document size detection")
    print(f"   • Adaptive chunk sizing (300-800 words)")
    print(f"   • Better content preservation")
    print(f"   • General document pattern recognition")
    print(f"   • Minimum content thresholds")
    
    # Ask for confirmation
    proceed = input(f"\n🔄 Clear database and reprocess {pdf_count} PDFs? (y/N): ").strip().lower()
    
    if proceed in ['y', 'yes']:
        # Clear database
        if clear_database():
            print(f"\n🔄 Starting reprocessing...")
            print(f"📝 You can monitor progress in another terminal with:")
            print(f"   python main_app.py --setup")
            print(f"\n⚠️  This will take some time for {pdf_count} files...")
            print(f"💡 You can check progress with: python main_app.py --info")
        else:
            print(f"❌ Database clearing failed. Cannot proceed.")
    else:
        print(f"⏭️  Skipping reprocessing. Database unchanged.")
        
        # Show current database status
        try:
            conn = psycopg2.connect(
                dbname=os.getenv("DB_NAME", "kforce"),
                user=os.getenv("DB_USER", "postgres"),
                password=os.getenv("DB_PASS", "kforce"),
                host=os.getenv("DB_HOST", "localhost"),
                port=os.getenv("DB_PORT", "5444")
            )
            
            with conn.cursor() as cur:
                cur.execute("SELECT COUNT(*) FROM documents")
                doc_count = cur.fetchone()[0]
                
                cur.execute("SELECT COUNT(DISTINCT filename) FROM documents")
                file_count = cur.fetchone()[0]
                
                print(f"\n📊 Current Database Status:")
                print(f"   📄 Total chunks: {doc_count}")
                print(f"   📁 Unique files: {file_count}")
                
                if doc_count > 0:
                    cur.execute("SELECT AVG(word_count) FROM documents")
                    avg_words = cur.fetchone()[0]
                    print(f"   📝 Average chunk size: {avg_words:.1f} words")
            
            conn.close()
            
        except Exception as e:
            print(f"❌ Error checking database: {e}")

if __name__ == "__main__":
    main()
