#!/usr/bin/env python3
"""
Demonstration of configurable training data paths
"""

import os
import shutil
from pathlib import Path
from dotenv import load_dotenv

def create_demo_folders():
    """Create demonstration folders with sample PDFs"""
    
    print("🔧 Creating demonstration folders...")
    
    # Create demo folders
    demo_folders = ["medical_docs", "veterinary_pdfs", "cattle_diseases"]
    
    for folder in demo_folders:
        folder_path = Path(folder)
        folder_path.mkdir(exist_ok=True)
        
        # Copy a few sample PDFs to each folder
        source_pdfs = ["ACIDOSIS.pdf", "PNEUMONIA.pdf", "ANEMIA.pdf"]
        
        for pdf in source_pdfs:
            source = Path("diseases") / pdf
            if source.exists():
                dest = folder_path / pdf
                if not dest.exists():
                    shutil.copy2(source, dest)
                    print(f"   Copied {pdf} to {folder}/")
    
    print("✅ Demo folders created!")

def test_training_path(folder_name):
    """Test processing with a specific training path"""
    
    print(f"\n🧪 Testing with training path: {folder_name}")
    print("-" * 40)
    
    # Check if folder exists
    folder_path = Path(folder_name)
    if not folder_path.exists():
        print(f"❌ Folder {folder_name} does not exist")
        return False
    
    # Count PDFs
    pdf_files = list(folder_path.glob("*.pdf"))
    print(f"📄 Found {len(pdf_files)} PDF files in {folder_name}")
    
    if not pdf_files:
        print("❌ No PDF files found")
        return False
    
    # Show files
    for pdf in pdf_files:
        print(f"   • {pdf.name}")
    
    return True

def show_env_usage():
    """Show how to use environment variables"""
    
    print("\n📋 How to Use Different Training Paths")
    print("=" * 50)
    
    print("1. **Default (diseases folder)**:")
    print("   TRAINING_DATA_PATH=diseases")
    print("   python main_app.py --setup")
    
    print("\n2. **Custom folder**:")
    print("   TRAINING_DATA_PATH=medical_docs")
    print("   python main_app.py --setup")
    
    print("\n3. **Absolute path**:")
    print("   TRAINING_DATA_PATH=/path/to/your/pdfs")
    print("   python main_app.py --setup")
    
    print("\n4. **Temporary override**:")
    print("   TRAINING_DATA_PATH=veterinary_pdfs python main_app.py --setup")

def demonstrate_config_change():
    """Demonstrate changing configuration"""
    
    print("\n🔄 Configuration Change Demonstration")
    print("=" * 50)
    
    # Load current config
    load_dotenv()
    current_path = os.getenv("TRAINING_DATA_PATH", "diseases")
    print(f"📁 Current training path: {current_path}")
    
    # Show how to change it
    print("\n💡 To change the training data path:")
    print("1. Edit the .env file")
    print("2. Change: TRAINING_DATA_PATH=your_new_folder")
    print("3. Run: python main_app.py --setup")
    
    print("\n⚠️  Important Notes:")
    print("• Each training path creates a separate set of embeddings")
    print("• Changing paths will process the new folder's PDFs")
    print("• Previous embeddings remain in the database")
    print("• Use --info to see what's currently in the database")

def cleanup_demo():
    """Clean up demonstration folders"""
    
    print("\n🧹 Cleaning up demonstration folders...")
    
    demo_folders = ["medical_docs", "veterinary_pdfs", "cattle_diseases"]
    
    for folder in demo_folders:
        folder_path = Path(folder)
        if folder_path.exists():
            shutil.rmtree(folder_path)
            print(f"   Removed {folder}/")
    
    print("✅ Cleanup complete!")

def main():
    """Main demonstration"""
    
    print("🏥 Medical Document Search System")
    print("📁 Training Data Path Configuration Demo")
    print("=" * 60)
    
    # Create demo folders
    create_demo_folders()
    
    # Test different paths
    demo_folders = ["diseases", "medical_docs", "veterinary_pdfs", "cattle_diseases"]
    
    for folder in demo_folders:
        test_training_path(folder)
    
    # Show usage instructions
    show_env_usage()
    
    # Demonstrate configuration
    demonstrate_config_change()
    
    # Ask if user wants to clean up
    print("\n" + "=" * 60)
    cleanup_choice = input("🗑️  Clean up demo folders? (y/N): ").strip().lower()
    
    if cleanup_choice in ['y', 'yes']:
        cleanup_demo()
    else:
        print("📁 Demo folders kept for testing")
    
    print("\n✅ Demo complete!")
    print("\n🚀 Ready to use configurable training paths!")
    print("   Edit TRAINING_DATA_PATH in .env file and run:")
    print("   python main_app.py --setup")

if __name__ == "__main__":
    main()
