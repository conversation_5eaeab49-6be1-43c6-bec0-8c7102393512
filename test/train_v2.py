import os
import fitz  # PyMuPDF
import psycopg2
from transformers import <PERSON>Tokenizer, AutoModel
import torch
import time
from datetime import datetime
import re
import numpy as np

# --- CONFIG ---
DB_NAME = "kforce"
DB_USER = "postgres"
DB_PASS = "kforce"
PDF_FOLDER = "pdf"  # Folder containing PDFs

# --- Nomic Embedder ---
class NomicEmbedder:
    def __init__(self):
        self.tokenizer = AutoTokenizer.from_pretrained(
            "nomic-ai/nomic-embed-text-v1", trust_remote_code=True
        )
        self.model = AutoModel.from_pretrained(
            "nomic-ai/nomic-embed-text-v1", trust_remote_code=True
        )
        self.model.eval()

    def encode(self, texts):
        with torch.no_grad():
            inputs = self.tokenizer(texts, padding=True, truncation=True, return_tensors="pt")
            outputs = self.model(**inputs)
            embeddings = outputs.last_hidden_state[:, 0, :]
            # Return as numpy array instead of list for better compatibility
            return embeddings.cpu().numpy()

# --- Extract Text from PDF ---
def extract_text_from_pdf(file_path):
    doc = fitz.open(file_path)
    text = "\n".join([page.get_text() for page in doc])
    doc.close()
    return text

# --- Enhanced Text Preprocessing ---
def preprocess_text(text):
    """Clean and preprocess text before chunking"""
    # Remove excessive whitespace
    text = re.sub(r'\s+', ' ', text)
    # Remove special characters that might interfere with search
    text = re.sub(r'[^\w\s.,!?;:()\-\'"]+', ' ', text)
    return text.strip()

# --- Extract Keywords for Indexing ---
def extract_keywords(text, min_length=3):
    """Extract meaningful keywords from text for better search indexing"""
    # Remove special characters and convert to lowercase
    text = re.sub(r'[^\w\s]', ' ', text.lower())
    words = text.split()
    
    # Filter out short words and common stop words
    stop_words = {'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 
                  'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 
                  'did', 'will', 'would', 'could', 'should', 'may', 'might', 'must', 'shall', 
                  'can', 'this', 'that', 'these', 'those', 'a', 'an', 'i', 'you', 'he', 'she', 
                  'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them'}
    
    keywords = [word for word in words if len(word) >= min_length and word not in stop_words]
    return list(set(keywords))  # Remove duplicates

# --- Improved Text Chunking ---
def split_text(text, chunk_size=500, overlap=50):
    """Split text into chunks with improved sentence boundary awareness"""
    # Preprocess text first
    text = preprocess_text(text)
    
    chunks = []
    sentences = re.split(r'[.!?]+', text)
    
    current_chunk = ""
    
    for sentence in sentences:
        sentence = sentence.strip()
        if not sentence:
            continue
            
        # If adding this sentence would exceed chunk size, save current chunk
        if len(current_chunk) + len(sentence) > chunk_size and current_chunk:
            chunks.append(current_chunk.strip())
            # Start new chunk with overlap
            overlap_text = current_chunk[-overlap:] if len(current_chunk) > overlap else current_chunk
            current_chunk = overlap_text + " " + sentence
        else:
            current_chunk += " " + sentence if current_chunk else sentence
    
    # Add the last chunk
    if current_chunk.strip():
        chunks.append(current_chunk.strip())
    
    # Fallback to simple chunking if sentence-based chunking produces chunks that are too large
    final_chunks = []
    for chunk in chunks:
        if len(chunk) <= chunk_size:
            final_chunks.append(chunk)
        else:
            # Split large chunks using simple method
            for i in range(0, len(chunk), chunk_size - overlap):
                final_chunks.append(chunk[i:i + chunk_size])
    
    return final_chunks

# --- Enhanced Database Storage ---
def store_embeddings(chunks, embedder, source_file):
    """Store embeddings with additional metadata for better search"""
    print(f"  Generating embeddings for {len(chunks)} chunks...")
    
    # Generate embeddings in batches for efficiency
    batch_size = 32
    all_vectors = []
    
    for i in range(0, len(chunks), batch_size):
        batch_chunks = chunks[i:i + batch_size]
        batch_vectors = embedder.encode(batch_chunks)
        all_vectors.extend(batch_vectors)
        print(f"    Processed {min(i + batch_size, len(chunks))}/{len(chunks)} chunks")
    
    print(f"  Storing embeddings in database...")
    
    conn = psycopg2.connect(
        dbname=DB_NAME, 
        user=DB_USER, 
        password=DB_PASS, 
        host="localhost", 
        port=5444
    )
    cur = conn.cursor()
    
    for i, (chunk, vector) in enumerate(zip(chunks, all_vectors)):
        # Extract keywords for this chunk
        keywords = extract_keywords(chunk)
        keywords_str = " ".join(keywords)  # Store as space-separated string
        
        # Calculate chunk statistics
        word_count = len(chunk.split())
        char_count = len(chunk)
        
        # Convert numpy array to list for PostgreSQL storage
        vector_list = vector.tolist()
        
        try:
            cur.execute("""
                INSERT INTO documents_v2 (content, embedding, source, keywords, word_count, char_count, chunk_index) 
                VALUES (%s, %s, %s, %s, %s, %s, %s)
            """, (chunk, vector_list, source_file, keywords_str, word_count, char_count, i))
        except Exception as e:
            print(f"    Error inserting chunk {i}: {e}")
            # If the enhanced insert fails, try the basic version
            try:
                cur.execute("""
                    INSERT INTO documents_v2 (content, embedding, source) 
                    VALUES (%s, %s, %s)
                """, (chunk, vector_list, source_file))
            except Exception as e2:
                print(f"    Error with basic insert for chunk {i}: {e2}")
                continue
    
    conn.commit()
    cur.close()
    conn.close()

# --- Database Schema Update ---
def update_database_schema():
    """Update database schema to support enhanced search features"""
    conn = psycopg2.connect(
        dbname=DB_NAME, 
        user=DB_USER, 
        password=DB_PASS, 
        host="localhost", 
        port=5444
    )
    cur = conn.cursor()
    
    try:
        # Add new columns if they don't exist
        cur.execute("""
            ALTER TABLE documents_v2 
            ADD COLUMN IF NOT EXISTS keywords TEXT,
            ADD COLUMN IF NOT EXISTS word_count INTEGER,
            ADD COLUMN IF NOT EXISTS char_count INTEGER,
            ADD COLUMN IF NOT EXISTS chunk_index INTEGER,
            ADD COLUMN IF NOT EXISTS created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
        """)
        
        # Create index on keywords for faster text search
        cur.execute("""
            CREATE INDEX IF NOT EXISTS idx_documents_keywords 
            ON documents USING gin(to_tsvector('english', keywords));
        """)
        
        # Create index on source for faster filtering
        cur.execute("""
            CREATE INDEX IF NOT EXISTS idx_documents_source 
            ON documents_v2(source);
        """)
        
        conn.commit()
        print("Database schema updated successfully.")
        
    except Exception as e:
        print(f"Error updating database schema: {e}")
        print("Continuing with basic schema...")
    
    cur.close()
    conn.close()

# --- Check if file already processed ---
def is_file_processed(source_file):
    """Check if a PDF file has already been processed"""
    conn = psycopg2.connect(
        dbname=DB_NAME, 
        user=DB_USER, 
        password=DB_PASS, 
        host="localhost", 
        port=5444
    )
    cur = conn.cursor()
    
    cur.execute("SELECT COUNT(*) FROM documents_v2 WHERE source = %s", (source_file,))
    count = cur.fetchone()[0]
    
    cur.close()
    conn.close()
    
    return count > 0

# --- MAIN TRAINING PIPELINE ---
if __name__ == "__main__":
    # Create pdf folder if it doesn't exist
    if not os.path.exists(PDF_FOLDER):
        os.makedirs(PDF_FOLDER)
        print(f"Created '{PDF_FOLDER}' folder. Please add PDF files to this folder.")
        exit()
    
    # Get list of PDF files
    pdf_files = [f for f in os.listdir(PDF_FOLDER) if f.lower().endswith('.pdf')]
    
    if not pdf_files:
        print(f"No PDF files found in '{PDF_FOLDER}' folder. Please add some PDF files.")
        exit()
    
    print(f"Found {len(pdf_files)} PDF files to process.")
    
    # Update database schema
    update_database_schema()
    
    # Initialize embedder
    print("Initializing embedding model...")
    embedder = NomicEmbedder()
    
    start_time = time.time()
    start_datetime = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"Starting embedding processing at: {start_datetime}")
    
    processed_count = 0
    skipped_count = 0
    
    # Process each PDF
    for i, pdf_file in enumerate(pdf_files, 1):
        file_path = os.path.join(PDF_FOLDER, pdf_file)
        
        print(f"\nProcessing {i}/{len(pdf_files)}: {pdf_file}")
        
        # Check if already processed
        if is_file_processed(pdf_file):
            print(f"  Skipping {pdf_file} (already processed)")
            skipped_count += 1
            continue
        
        try:
            # Extract text
            print(f"  Extracting text...")
            text = extract_text_from_pdf(file_path)
            
            if not text.strip():
                print(f"  Warning: No text extracted from {pdf_file}")
                continue
            
            # Split into chunks
            print(f"  Splitting into chunks...")
            chunks = split_text(text)
            print(f"  Created {len(chunks)} chunks")
            
            # Store embeddings
            store_embeddings(chunks, embedder, pdf_file)
            
            processed_count += 1
            current_datetime = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            elapsed_time = time.time() - start_time
            print(f"  Completed {pdf_file} at {current_datetime}")
            print(f"  Elapsed time: {elapsed_time:.2f} seconds")
            
        except Exception as e:
            print(f"  Error processing {pdf_file}: {e}")
            continue
    
    end_time = time.time()
    end_datetime = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    total_time = end_time - start_time
    
    print(f"\n=== Processing Complete ===")
    print(f"Finished embedding processing at: {end_datetime}")
    print(f"Total time taken: {total_time:.2f} seconds")
    print(f"Files processed: {processed_count}")
    print(f"Files skipped (already processed): {skipped_count}")
    print(f"Average time per file: {total_time/max(processed_count, 1):.2f} seconds")
    
    if processed_count > 0:
        print("All new PDF files have been processed and embeddings stored in the database.")
    else:
        print("No new files were processed.")