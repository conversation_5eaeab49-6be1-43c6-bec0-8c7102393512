#!/usr/bin/env python3
"""
Main Application for PDF Medical Document Search System
Combines PDF processing, embedding, and search functionality
"""

import argparse
import sys
import os
from pathlib import Path
from dotenv import load_dotenv
from pdf_embedder import PDFEmbeddingPipeline
from search_system import MedicalSearchSystem

# Load environment variables
load_dotenv()

def setup_database():
    """Setup database and process PDFs"""
    print("🔧 Setting up database and processing PDFs...")
    
    pipeline = PDFEmbeddingPipeline()
    
    # Process training data folder from environment
    training_folder = os.getenv("TRAINING_DATA_PATH", "diseases")
    if not Path(training_folder).exists():
        print(f"❌ Error: {training_folder} folder not found!")
        print(f"💡 Set TRAINING_DATA_PATH in .env file to specify a different folder")
        return False
        
    pipeline.process_pdf_folder(training_folder)
    return True

def run_search_interface():
    """Run the interactive search interface"""
    print("🚀 Starting Medical Document Search System...")
    
    search_system = MedicalSearchSystem()
    
    # Check if database has documents
    stats = search_system.get_database_stats()
    if "error" in stats or stats.get("total_documents", 0) == 0:
        print("❌ No documents found in database. Please run setup first.")
        print("Usage: python main_app.py --setup")
        return
    
    print("🏥 Medical Document Search System")
    print("=" * 50)
    print(f"📊 Database contains {stats['total_documents']} document chunks from {stats['unique_files']} files")
    print("\nAvailable documents:")
    for file_info in stats['files'][:10]:  # Show first 10 files
        print(f"  • {file_info['filename']} ({file_info['chunks']} chunks)")
    
    if len(stats['files']) > 10:
        print(f"  ... and {len(stats['files']) - 10} more files")
    
    print("\n" + "=" * 50)
    
    while True:
        try:
            # Get user query
            query = input("\n🔍 Enter your medical question (or 'quit' to exit): ").strip()
            
            if query.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break
                
            if not query:
                continue
                
            # Search and get answer
            print("\n⏳ Searching documents and generating response...")
            result = search_system.search_and_answer(query)
            
            # Display results
            print(f"\n📋 Found {result['documents_found']} relevant documents")
            print("\n🤖 AI Response:")
            print("=" * 50)
            print(result['answer'])
            print("=" * 50)
            
            # Show sources
            if result['sources']:
                print(f"\n📚 Sources used:")
                for i, source in enumerate(result['sources'][:5], 1):
                    print(f"  {i}. {source['filename']} (relevance: {source['similarity_score']:.1%})")
            
            # Ask if user wants to see the raw context
            show_context = input("\n📄 Show raw document context? (y/N): ").strip().lower()
            if show_context in ['y', 'yes']:
                print("\n📄 Raw Context:")
                print("-" * 50)
                print(result['context'][:2000] + "..." if len(result['context']) > 2000 else result['context'])
                print("-" * 50)
            
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

def test_single_pdf():
    """Test processing a single PDF"""
    print("🧪 Testing single PDF processing...")

    # Find first PDF in training data folder
    training_folder = Path(os.getenv("TRAINING_DATA_PATH", "diseases"))
    pdf_files = list(training_folder.glob("*.pdf"))

    if not pdf_files:
        print(f"❌ No PDF files found in {training_folder} folder")
        return
        
    test_pdf = pdf_files[0]
    print(f"📄 Testing with: {test_pdf.name}")
    
    pipeline = PDFEmbeddingPipeline()
    pipeline.db_manager.create_tables()
    pipeline.process_single_pdf(str(test_pdf))
    
    print("✅ Test completed!")

def show_database_info():
    """Show database information"""
    search_system = MedicalSearchSystem()
    stats = search_system.get_database_stats()
    
    if "error" in stats:
        print(f"❌ Database error: {stats['error']}")
        return
        
    print("📊 Database Information")
    print("=" * 30)
    print(f"Total document chunks: {stats['total_documents']}")
    print(f"Unique files: {stats['unique_files']}")
    print("\nFiles in database:")
    
    for file_info in stats['files']:
        print(f"  • {file_info['filename']} - {file_info['chunks']} chunks")

def main():
    """Main application entry point"""
    parser = argparse.ArgumentParser(
        description="Medical Document Search System",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main_app.py                    # Run interactive search
  python main_app.py --setup           # Setup database and process PDFs
  python main_app.py --test            # Test with single PDF
  python main_app.py --info            # Show database information
        """
    )
    
    parser.add_argument(
        "--setup",
        action="store_true",
        help="Setup database and process all PDFs in training data folder (set TRAINING_DATA_PATH in .env)"
    )
    
    parser.add_argument(
        "--test",
        action="store_true", 
        help="Test processing with a single PDF"
    )
    
    parser.add_argument(
        "--info",
        action="store_true",
        help="Show database information"
    )
    
    args = parser.parse_args()
    
    if args.setup:
        success = setup_database()
        if success:
            print("✅ Setup completed successfully!")
            print("You can now run the search interface with: python main_app.py")
        else:
            print("❌ Setup failed!")
            sys.exit(1)
            
    elif args.test:
        test_single_pdf()
        
    elif args.info:
        show_database_info()
        
    else:
        # Default: run search interface
        run_search_interface()

if __name__ == "__main__":
    main()
