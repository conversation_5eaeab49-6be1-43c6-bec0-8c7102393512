"""
Improved Chunking Strategy for Medical Documents
Based on analysis of veterinary PDF structure
"""

import re
import hashlib
from typing import List, Dict
import fitz

class ImprovedMedicalChunker:
    """Advanced chunking strategy specifically designed for medical documents"""
    
    def __init__(self):
        # Medical section keywords (expanded based on veterinary documents)
        self.medical_sections = [
            'symptoms', 'signs', 'clinical signs', 'clinical symptoms',
            'treatment', 'therapy', 'management', 'medication',
            'diagnosis', 'diagnostic', 'differential diagnosis',
            'occurrence', 'epidemiology', 'prevalence', 'incidence',
            'prevention', 'prophylaxis', 'control measures',
            'etiology', 'causes', 'causative agent', 'pathogen',
            'pathogenesis', 'pathology', 'pathophysiology',
            'prognosis', 'outcome', 'recovery',
            'overview', 'introduction', 'definition', 'description',
            'complications', 'sequelae', 'adverse effects',
            'vaccination', 'immunization', 'biosecurity'
        ]
        
        # Patterns for identifying section headers
        self.section_patterns = [
            r'^#{1,3}\s*(.+)$',                    # Markdown headers
            r'^([A-Z][A-Z\s]{2,}):?\s*$',         # All caps headers
            r'^(\d+\.?\s*[A-Z][^.]*):?\s*$',      # Numbered sections
            r'^([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*):?\s*$',  # Title case headers
        ]
        
        # Patterns for list items
        self.list_patterns = [
            r'^[•\-\*]\s+(.+)$',                  # Bullet points
            r'^\d+\)\s+(.+)$',                    # Numbered lists
            r'^[a-z]\)\s+(.+)$',                  # Lettered lists
        ]
        
        # Patterns for special content
        self.special_patterns = {
            'dosage': r'(?i)(dose|dosage|mg/kg|ml/kg|\d+\s*mg|\d+\s*ml)',
            'temperature': r'(?i)(temperature|fever|\d+°[CF]|\d+\s*degrees)',
            'duration': r'(?i)(days?|weeks?|months?|hours?|\d+\s*times?)',
            'age_group': r'(?i)(calf|calves|adult|cow|bull|heifer|young|old)',
        }
    
    def extract_text_from_pdf(self, pdf_path: str) -> str:
        """Extract text from PDF with better formatting preservation"""
        try:
            doc = fitz.open(pdf_path)
            text = ""
            
            for page_num in range(doc.page_count):
                page = doc[page_num]
                # Get text with layout preservation
                text += page.get_text("text")
                
            doc.close()
            return text.strip()
            
        except Exception as e:
            print(f"Error extracting text from {pdf_path}: {e}")
            return ""
    
    def identify_section_headers(self, lines: List[str]) -> List[Dict]:
        """Identify section headers using multiple patterns"""
        headers = []
        
        for i, line in enumerate(lines):
            line_stripped = line.strip()
            if not line_stripped:
                continue
            
            # Check against section patterns
            for pattern in self.section_patterns:
                match = re.match(pattern, line_stripped)
                if match:
                    header_text = match.group(1) if match.groups() else line_stripped
                    
                    # Check if it's a medical section
                    is_medical_section = any(
                        keyword in header_text.lower() 
                        for keyword in self.medical_sections
                    )
                    
                    headers.append({
                        'line_index': i,
                        'text': header_text,
                        'original_line': line_stripped,
                        'is_medical_section': is_medical_section,
                        'pattern_type': 'header'
                    })
                    break
        
        return headers
    
    def identify_list_items(self, lines: List[str]) -> List[Dict]:
        """Identify list items and group them"""
        list_items = []
        
        for i, line in enumerate(lines):
            line_stripped = line.strip()
            
            for pattern in self.list_patterns:
                match = re.match(pattern, line_stripped)
                if match:
                    list_items.append({
                        'line_index': i,
                        'text': match.group(1),
                        'original_line': line_stripped,
                        'pattern_type': 'list_item'
                    })
                    break
        
        return list_items
    
    def analyze_content_type(self, text: str) -> Dict:
        """Analyze what type of medical content this is"""
        content_analysis = {
            'has_symptoms': False,
            'has_treatment': False,
            'has_dosage': False,
            'has_temperature': False,
            'has_duration': False,
            'mentions_age_group': False,
            'content_density': len(text.split()) / max(len(text.split('\n')), 1)
        }
        
        text_lower = text.lower()
        
        # Check for medical content types
        if any(word in text_lower for word in ['symptom', 'sign', 'present', 'show', 'exhibit']):
            content_analysis['has_symptoms'] = True
            
        if any(word in text_lower for word in ['treat', 'therapy', 'medication', 'drug', 'antibiotic']):
            content_analysis['has_treatment'] = True
        
        # Check for special patterns
        for pattern_name, pattern in self.special_patterns.items():
            if re.search(pattern, text):
                content_analysis[f'has_{pattern_name}'] = True
        
        return content_analysis
    
    def create_smart_chunks(self, text: str, filename: str) -> List[Dict]:
        """Create intelligent chunks based on medical document structure"""
        if not text:
            return []
        
        lines = text.split('\n')
        lines = [line for line in lines if line.strip()]  # Remove empty lines
        
        # Identify structural elements
        headers = self.identify_section_headers(lines)
        list_items = self.identify_list_items(lines)
        
        chunks = []
        current_chunk_lines = []
        current_heading = "Introduction"
        chunk_index = 0
        
        # Sort headers by line index
        headers.sort(key=lambda x: x['line_index'])
        
        i = 0
        while i < len(lines):
            line = lines[i].strip()
            
            # Check if this line is a header
            is_header = any(h['line_index'] == i for h in headers)
            
            if is_header:
                # Save previous chunk if it has content
                if current_chunk_lines:
                    chunk_text = '\n'.join(current_chunk_lines).strip()
                    if chunk_text:
                        chunk = self._create_chunk(
                            chunk_text, filename, chunk_index, current_heading
                        )
                        chunks.append(chunk)
                        chunk_index += 1
                
                # Start new chunk
                header_info = next(h for h in headers if h['line_index'] == i)
                current_heading = header_info['text']
                current_chunk_lines = [line]
                
            else:
                # Add line to current chunk
                current_chunk_lines.append(line)
                
                # Check if chunk is getting too long (adaptive sizing)
                chunk_text = '\n'.join(current_chunk_lines)
                word_count = len(chunk_text.split())
                
                # Adaptive chunk size based on content type
                max_words = self._get_adaptive_chunk_size(chunk_text, current_heading)
                
                if word_count > max_words:
                    # Try to find a good break point
                    break_point = self._find_break_point(current_chunk_lines)
                    
                    if break_point > 0:
                        # Split at break point
                        chunk_lines = current_chunk_lines[:break_point]
                        remaining_lines = current_chunk_lines[break_point:]
                        
                        chunk_text = '\n'.join(chunk_lines).strip()
                        if chunk_text:
                            chunk = self._create_chunk(
                                chunk_text, filename, chunk_index, current_heading
                            )
                            chunks.append(chunk)
                            chunk_index += 1
                        
                        current_chunk_lines = remaining_lines
            
            i += 1
        
        # Add final chunk
        if current_chunk_lines:
            chunk_text = '\n'.join(current_chunk_lines).strip()
            if chunk_text:
                chunk = self._create_chunk(
                    chunk_text, filename, chunk_index, current_heading
                )
                chunks.append(chunk)
        
        return chunks
    
    def _get_adaptive_chunk_size(self, text: str, heading: str) -> int:
        """Get adaptive chunk size based on content type"""
        base_size = 800  # Base word count
        
        # Adjust based on heading type
        heading_lower = heading.lower()
        
        if any(word in heading_lower for word in ['symptom', 'sign']):
            return base_size + 200  # Symptoms can be longer
        elif any(word in heading_lower for word in ['treatment', 'therapy']):
            return base_size + 300  # Treatment details can be longer
        elif any(word in heading_lower for word in ['overview', 'introduction']):
            return base_size - 200  # Keep overviews shorter
        elif any(word in heading_lower for word in ['diagnosis', 'diagnostic']):
            return base_size + 100  # Diagnostic info moderate length
        
        return base_size
    
    def _find_break_point(self, lines: List[str]) -> int:
        """Find a good break point in the middle of a chunk"""
        if len(lines) < 5:
            return len(lines)
        
        # Look for natural break points (empty lines, list items, etc.)
        for i in range(len(lines) // 2, len(lines) - 2):
            line = lines[i].strip()
            next_line = lines[i + 1].strip() if i + 1 < len(lines) else ""
            
            # Break after list items
            if re.match(r'^[•\-\*]\s+', line):
                return i + 1
            
            # Break before new list items
            if re.match(r'^[•\-\*]\s+', next_line):
                return i + 1
            
            # Break at sentence endings
            if line.endswith('.') and not line.endswith('etc.') and not line.endswith('Dr.'):
                return i + 1
        
        # Default break point
        return len(lines) // 2
    
    def _create_chunk(self, text: str, filename: str, chunk_index: int, heading: str) -> Dict:
        """Create a chunk with metadata"""
        chunk_id = hashlib.md5(f"{filename}_{chunk_index}_{heading}_{text[:50]}".encode()).hexdigest()
        
        # Analyze content
        content_analysis = self.analyze_content_type(text)
        
        return {
            "chunk_id": chunk_id,
            "filename": filename,
            "chunk_index": chunk_index,
            "text": text,
            "heading": heading,
            "word_count": len(text.split()),
            "content_analysis": content_analysis
        }

# Test the improved chunker
if __name__ == "__main__":
    chunker = ImprovedMedicalChunker()
    
    # Test with a sample PDF
    test_pdf = "diseases/ACIDOSIS.pdf"
    text = chunker.extract_text_from_pdf(test_pdf)
    
    if text:
        chunks = chunker.create_smart_chunks(text, "ACIDOSIS.pdf")
        
        print(f"📄 Improved Chunking Results for ACIDOSIS.pdf:")
        print(f"Created {len(chunks)} chunks")
        
        for i, chunk in enumerate(chunks, 1):
            print(f"\nChunk {i}: {chunk['heading']} ({chunk['word_count']} words)")
            print(f"Content: {chunk['text'][:150]}...")
            print(f"Analysis: {chunk['content_analysis']}")
    else:
        print("Failed to extract text from PDF")
