import fitz  # PyMuPDF
import psycopg2
from transformers import AutoTokenizer, AutoModel
import torch
from ollama import chat
import time
from datetime import datetime
import numpy as np
from collections import Counter
import re
from typing import List, Dict, Tuple
import json

# --- CONFIG ---
DB_NAME = "kforce"
DB_USER = "postgres"
DB_PASS = "kforce"
OLLAMA_MODEL = "llama3.2"

# --- Nomic Embedder ---
class NomicEmbedder:
    def __init__(self):
        self.tokenizer = AutoTokenizer.from_pretrained(
            "nomic-ai/nomic-embed-text-v1", trust_remote_code=True
        )
        self.model = AutoModel.from_pretrained(
            "nomic-ai/nomic-embed-text-v1", trust_remote_code=True
        )
        self.model.eval()

    def encode(self, texts):
        if isinstance(texts, str):
            texts = [texts]
            
        with torch.no_grad():
            inputs = self.tokenizer(texts, padding=True, truncation=True, 
                                  return_tensors="pt", max_length=512)
            outputs = self.model(**inputs)
            embeddings = outputs.last_hidden_state[:, 0, :]
            # Normalize embeddings for cosine similarity
            embeddings = torch.nn.functional.normalize(embeddings, p=2, dim=1)
            return embeddings.cpu().numpy()

# --- Cosine Similarity Function ---
def cosine_similarity(vec1, vec2):
    """Calculate cosine similarity between two vectors"""
    if isinstance(vec1, list):
        vec1 = np.array(vec1)
    if isinstance(vec2, list):
        vec2 = np.array(vec2)
    
    # Vectors are already normalized, so dot product = cosine similarity
    return np.dot(vec1, vec2)

# --- Enhanced Query Processing ---
def extract_query_keywords(question: str) -> List[str]:
    """Extract important keywords from the query for occurrence counting"""
    # Remove common stop words and extract meaningful terms
    stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 
                  'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 
                  'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should',
                  'what', 'how', 'when', 'where', 'why', 'who', 'which'}
    
    # Extract words and clean them
    words = re.findall(r'\b[a-zA-Z]{3,}\b', question.lower())
    keywords = [word for word in words if word not in stop_words]
    
    # Also extract potential medical/technical terms
    technical_terms = re.findall(r'\b[A-Z]{2,}[a-z]*\b', question)
    keywords.extend([term.lower() for term in technical_terms])
    
    return list(set(keywords))  # Remove duplicates

# --- Advanced Similarity Search ---
def query_similar_chunks_enhanced(question: str, embedder, top_k=10, similarity_threshold=0.3):
    """Enhanced similarity search with cosine similarity and occurrence counting"""
    
    # Generate query embedding
    query_vec = embedder.encode([question])[0]
    query_keywords = extract_query_keywords(question)
    
    print(f"🔍 Query keywords: {query_keywords}")
    
    conn = psycopg2.connect(dbname=DB_NAME, user=DB_USER, password=DB_PASS,
                           host="localhost", port=5444)
    cur = conn.cursor()
    
    # Get all relevant chunks with metadata
    cur.execute("""
    SELECT chunk_id, content, embedding, source, chunk_type, section, 
           importance, metadata
    FROM documents_enhanced
    """)
    
    results = cur.fetchall()
    cur.close()
    conn.close()
    
    if not results:
        print("No documents found in database")
        return [], {}
    
    # Calculate similarities and occurrence scores
    scored_results = []
    
    for row in results:
        chunk_id, content, embedding, source, chunk_type, section, importance, metadata = row
        
        # Convert embedding from database format to numpy array
        if isinstance(embedding, str):
            # Handle string format if needed
            embedding = np.array([float(x) for x in embedding.strip('[]').split(',')])
        elif hasattr(embedding, '__iter__'):
            embedding = np.array(embedding)
        
        # Calculate cosine similarity
        cos_sim = cosine_similarity(query_vec, embedding)
        
        # Calculate keyword occurrence score
        content_lower = content.lower()
        keyword_occurrences = {}
        total_occurrences = 0
        
        for keyword in query_keywords:
            count = content_lower.count(keyword)
            if count > 0:
                keyword_occurrences[keyword] = count
                total_occurrences += count
        
        # Calculate occurrence score (normalized by content length)
        occurrence_score = total_occurrences / max(len(content.split()), 1) * 10
        
        # Importance weight
        importance_weights = {'high': 1.2, 'medium': 1.0, 'low': 0.8}
        importance_weight = importance_weights.get(importance, 1.0)
        
        # Chunk type weight
        chunk_type_weights = {
            'metadata': 1.1,
            'treatment': 1.2,
            'section': 1.0,
            'full_context': 0.9
        }
        chunk_type_weight = chunk_type_weights.get(chunk_type, 1.0)
        
        # Combined score
        combined_score = (cos_sim * 0.7 + occurrence_score * 0.3) * importance_weight * chunk_type_weight
        
        if cos_sim >= similarity_threshold:
            scored_results.append({
                'chunk_id': chunk_id,
                'content': content,
                'source': source,
                'chunk_type': chunk_type,
                'section': section,
                'importance': importance,
                'cosine_similarity': float(cos_sim),
                'occurrence_score': occurrence_score,
                'keyword_occurrences': keyword_occurrences,
                'combined_score': combined_score,
                'metadata': metadata
            })
    
    # Sort by combined score
    scored_results.sort(key=lambda x: x['combined_score'], reverse=True)
    
    # Prepare statistics
    stats = {
        'total_chunks_searched': len(results),
        'chunks_above_threshold': len(scored_results),
        'query_keywords': query_keywords,
        'avg_similarity': np.mean([r['cosine_similarity'] for r in scored_results]) if scored_results else 0,
        'max_similarity': max([r['cosine_similarity'] for r in scored_results]) if scored_results else 0,
        'total_keyword_matches': sum([sum(r['keyword_occurrences'].values()) for r in scored_results])
    }
    
    return scored_results[:top_k], stats

# --- Enhanced Answer Generation ---
def ask_ollama_enhanced(question: str, context_chunks: List[Dict], stats: Dict):
    """Enhanced answer generation with context awareness"""
    
    # Prepare rich context
    context_parts = []
    
    # Group chunks by source for better organization
    sources = {}
    for chunk in context_chunks:
        source = chunk['source']
        if source not in sources:
            sources[source] = []
        sources[source].append(chunk)
    
    # Build context with source organization
    for source, chunks in sources.items():
        context_parts.append(f"\n--- Document: {source} ---")
        for chunk in chunks:
            context_parts.append(f"[{chunk['chunk_type'].upper()}] {chunk['content']}")
            if chunk['keyword_occurrences']:
                context_parts.append(f"Key matches: {chunk['keyword_occurrences']}")
    
    context = "\n".join(context_parts)
    
    # Enhanced prompt with statistics
    prompt = f"""
You are an expert veterinary assistant analyzing animal health complaint documents. Don't recommend any other information other than the context provided.

SEARCH STATISTICS:
- Searched {stats['total_chunks_searched']} document chunks
- Found {stats['chunks_above_threshold']} relevant chunks above similarity threshold
- Average similarity score: {stats['avg_similarity']:.3f}
- Maximum similarity score: {stats['max_similarity']:.3f}
- Total keyword matches: {stats['total_keyword_matches']}
- Query keywords analyzed: {', '.join(stats['query_keywords'])}

RELEVANT CONTEXT:
{context}

QUESTION: {question}

INSTRUCTIONS:
1. Provide a comprehensive answer based on the context above
2. If you find specific cases/documents relevant to the question, mention them by name
3. Include relevant details like animal information, treatments, or complaint IDs when available
4. If multiple documents are relevant, compare or summarize the findings
5. Be specific about medical treatments, dosages, and procedures when mentioned
6. If the context doesn't fully answer the question, clearly state what information is missing

ANSWER:
"""
    
    try:
        response = chat(model=OLLAMA_MODEL, messages=[{"role": "user", "content": prompt}])
        return response['message']['content']
    except Exception as e:
        return f"Error generating response: {e}"

# --- MAIN PIPELINE ---
if __name__ == "__main__":
    print("🚀 Enhanced Veterinary Document Query System")
    print("=" * 50)
    
    # Initialize embedder
    print("📊 Initializing embedder...")
    embedder = NomicEmbedder()
    
    while True:
        # Ask a question
        print("\n" + "="*50)
        question = input("Enter your question (or 'quit' to exit): ").strip()
        
        if question.lower() in ['quit', 'exit', 'q']:
            print("Goodbye! 👋")
            break
            
        if not question:
            print("Please enter a valid question.")
            continue
        
        start_time = time.time()
        start_datetime = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"🕐 Starting query processing at: {start_datetime}")
        
        # Enhanced similarity search
        relevant_chunks, stats = query_similar_chunks_enhanced(question, embedder, top_k=8)
        
        if not relevant_chunks:
            print("❌ No relevant documents found for your query.")
            continue
        
        search_time = time.time()
        print(f"⚡ Search completed in {search_time - start_time:.2f} seconds")
        
        # Display search statistics
        print(f"\n📈 SEARCH RESULTS:")
        print(f"   • Found {len(relevant_chunks)} relevant chunks")
        print(f"   • Average similarity: {stats['avg_similarity']:.3f}")
        print(f"   • Best match similarity: {stats['max_similarity']:.3f}")
        print(f"   • Total keyword occurrences: {stats['total_keyword_matches']}")
        
        # Show top results summary
        print(f"\n🔍 TOP MATCHES:")
        for i, chunk in enumerate(relevant_chunks[:3], 1):
            print(f"   {i}. {chunk['source']} [{chunk['chunk_type']}] - Similarity: {chunk['cosine_similarity']:.3f}")
            if chunk['keyword_occurrences']:
                print(f"      Keywords found: {chunk['keyword_occurrences']}")
        
        # Generate enhanced answer
        print(f"\n🤖 Generating answer...")
        answer = ask_ollama_enhanced(question, relevant_chunks, stats)
        
        end_time = time.time()
        end_datetime = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        print(f"\n" + "="*50)
        print("📋 ANSWER:")
        print("="*50)
        print(answer)
        print("="*50)
        print(f"⏱️  Total processing time: {end_time - start_time:.2f} seconds")
        print(f"🕐 Completed at: {end_datetime}")