import os
from datetime import datetime
from llama_index.readers.file import PyMuPDFReader
from llama_index.vector_stores.postgres import PGVectorStore
from llama_index.core import Document, VectorStoreIndex, StorageContext
from transformers import AutoTokenizer, AutoModel
from llama_index.core.embeddings import BaseEmbedding
import torch

# --- Custom Nomic Embedder ---
class NomicEmbedding(BaseEmbedding):
    def __init__(self, model_name="nomic-ai/nomic-embed-text-v1", **kwargs):
        super().__init__(**kwargs)
        # Use object.__setattr__ to bypass Pydantic validation
        object.__setattr__(self, 'tokenizer', AutoTokenizer.from_pretrained(model_name, trust_remote_code=True))
        object.__setattr__(self, 'model', AutoModel.from_pretrained(model_name, trust_remote_code=True))
        self.model.eval()

    class Config:
        arbitrary_types_allowed = True

    def _get_embedding(self, text: str):
        with torch.no_grad():
            inputs = self.tokenizer(text, padding=True, truncation=True, return_tensors="pt")
            outputs = self.model(**inputs)
            return outputs.last_hidden_state[:, 0, :].squeeze(0).cpu().tolist()

    def _get_text_embedding(self, text: str):
        """Get embedding for a single text"""
        return self._get_embedding(text)

    def _get_text_embeddings(self, texts):
        """Get embeddings for multiple texts"""
        return [self._get_embedding(text) for text in texts]

    def _get_query_embedding(self, query: str):
        """Get embedding for a query"""
        return self._get_embedding(query)

    async def _aget_query_embedding(self, query: str):
        """Async version of get_query_embedding"""
        return self._get_query_embedding(query)

# --- CONFIG ---
PDF_FOLDER = "diseases"
DB_NAME = "kforce"
DB_USER = "postgres"
DB_PASS = "kforce"
DB_HOST = "localhost"
DB_PORT = "5444"
DB_TABLE = "documents"

# --- Ensure folder exists ---
if not os.path.exists(PDF_FOLDER):
    os.makedirs(PDF_FOLDER)
    print(f"Created folder '{PDF_FOLDER}'")
    exit()

pdf_files = [f for f in os.listdir(PDF_FOLDER) if f.lower().endswith(".pdf")]
if not pdf_files:
    print("No PDF files found.")
    exit()

# --- Initialize components ---
embed_model = NomicEmbedding()
vector_store = PGVectorStore.from_params(
    database=DB_NAME,
    user=DB_USER,
    password=DB_PASS,
    host=DB_HOST,
    port=int(DB_PORT),
    table_name=DB_TABLE,
    embed_dim=768
)
storage_context = StorageContext.from_defaults(vector_store=vector_store)
reader = PyMuPDFReader()

# --- Load & Combine Documents (No Chunking) ---
all_docs = []
for i, pdf_file in enumerate(pdf_files, 1):
    path = os.path.join(PDF_FOLDER, pdf_file)
    print(f"[{i}/{len(pdf_files)}] Reading {pdf_file}")

    try:
        raw_docs = reader.load_data(file_path=path)
        # Combine all pages into a single text
        combined_text = "\n\n".join([doc.text.strip() for doc in raw_docs if doc.text.strip()])
        
        if combined_text:
            # Create single document for entire PDF
            pdf_doc = Document(
                text=combined_text, 
                metadata={
                    "source": pdf_file,
                    "total_pages": len(raw_docs),
                    "processed_at": datetime.now().isoformat()
                }
            )
            all_docs.append(pdf_doc)
            print(f"   ➜ Combined {len(raw_docs)} pages into 1 document")
        else:
            print(f"   ⚠️ No valid text found in {pdf_file}")
            
    except Exception as e:
        print(f"   ⚠️ Error: {e}")

if not all_docs:
    print("❌ No valid documents found. Exiting.")
    exit()

# --- Index into pgvector ---
print(f"\n📡 Embedding and storing {len(all_docs)} documents...")
index = VectorStoreIndex(
    all_docs,
    embed_model=embed_model,
    storage_context=storage_context,
)

print(f"\n✅ Done. Indexed {len(all_docs)} PDFs as complete documents.")
print(f"Total documents in index: {len(all_docs)}")