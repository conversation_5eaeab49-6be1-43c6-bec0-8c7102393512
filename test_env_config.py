#!/usr/bin/env python3
"""
Test environment configuration for training data path
"""

import os
from pathlib import Path
from dotenv import load_dotenv

def test_training_path_config():
    """Test the training data path configuration"""
    
    print("🧪 Testing Training Data Path Configuration")
    print("=" * 50)
    
    # Load environment variables
    load_dotenv()
    
    # Get training data path
    training_path = os.getenv("TRAINING_DATA_PATH", "diseases")
    print(f"📁 Training data path from .env: {training_path}")
    
    # Check if path exists
    path_obj = Path(training_path)
    if path_obj.exists():
        print(f"✅ Path exists: {path_obj.absolute()}")
        
        # Count PDF files
        pdf_files = list(path_obj.glob("*.pdf"))
        print(f"📄 Found {len(pdf_files)} PDF files")
        
        if pdf_files:
            print("📋 Sample files:")
            for i, pdf_file in enumerate(pdf_files[:5], 1):
                print(f"   {i}. {pdf_file.name}")
            if len(pdf_files) > 5:
                print(f"   ... and {len(pdf_files) - 5} more files")
        
        return True
    else:
        print(f"❌ Path does not exist: {path_obj.absolute()}")
        print("💡 Create the folder or update TRAINING_DATA_PATH in .env file")
        return False

def test_different_paths():
    """Test with different training data paths"""
    
    print("\n🔄 Testing Different Path Configurations")
    print("=" * 50)
    
    test_paths = [
        "diseases",
        "documents", 
        "pdfs",
        "medical_docs"
    ]
    
    for test_path in test_paths:
        print(f"\n📁 Testing path: {test_path}")
        path_obj = Path(test_path)
        
        if path_obj.exists():
            pdf_count = len(list(path_obj.glob("*.pdf")))
            print(f"   ✅ Exists with {pdf_count} PDF files")
        else:
            print(f"   ❌ Does not exist")

def show_current_config():
    """Show current configuration"""
    
    print("\n📋 Current Configuration")
    print("=" * 30)
    
    load_dotenv()
    
    config_vars = [
        "TRAINING_DATA_PATH",
        "DB_NAME",
        "DB_USER", 
        "GROQ_API_KEY",
        "OLLAMA_BASE_URL",
        "EMBEDDING_MODEL"
    ]
    
    for var in config_vars:
        value = os.getenv(var, "Not set")
        if "API_KEY" in var and value != "Not set":
            # Mask API keys for security
            masked_value = f"{value[:10]}...{value[-4:]}" if len(value) > 14 else "***"
            print(f"   {var}: {masked_value}")
        else:
            print(f"   {var}: {value}")

def main():
    """Main test function"""
    
    # Test current configuration
    success = test_training_path_config()
    
    # Test different paths
    test_different_paths()
    
    # Show current config
    show_current_config()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ Training data path configuration is working correctly!")
        print("You can now run: python main_app.py --setup")
    else:
        print("⚠️  Please check your training data path configuration.")
        print("Update TRAINING_DATA_PATH in .env file if needed.")
    
    print("\n💡 To use a different folder:")
    print("   1. Create your PDF folder (e.g., 'my_documents')")
    print("   2. Add PDFs to the folder")
    print("   3. Set TRAINING_DATA_PATH=my_documents in .env file")
    print("   4. Run: python main_app.py --setup")

if __name__ == "__main__":
    main()
