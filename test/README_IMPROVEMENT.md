# RAG System Improvement Guide

## 🎯 Problem
The query "Lateral recumbency with curved neck on lateral side of body" is not returning the expected "MILK FEVER" document.

## 🔧 Solution Overview
This improvement package provides:

1. **Document Analysis** - Analyze all PDFs to understand content
2. **Enhanced Indexing** - Create multiple chunk types for better search
3. **Query Expansion** - Add medical synonyms and related terms
4. **Comprehensive Testing** - Validate improvements

## 📋 Files Created

### Core Improvement Scripts
- `analyze_documents.py` - Analyze PDF content and extract key terms
- `improved_indexing.py` - Create enhanced index with medical-focused chunking
- `test_improved_search.py` - Test the improved search system
- `run_improvement_pipeline.py` - Run complete improvement process

### Diagnostic Scripts
- `debug_search.py` - Debug current search issues
- `test_milk_fever_search.py` - Comprehensive milk fever search testing
- `improve_search.py` - Analyze MILK FEVER document content

### Updated Main System
- `main.llamaindex.py` - Updated with query expansion and improved display

## 🚀 Quick Start

### Option 1: Run Complete Pipeline (Recommended)
```bash
python run_improvement_pipeline.py
```

### Option 2: Step by Step
```bash
# 1. Analyze documents
python analyze_documents.py

# 2. Create improved index
python improved_indexing.py

# 3. Test improved search
python test_improved_search.py

# 4. Use improved system
python main.llamaindex.py
```

## 🔍 Key Improvements

### 1. Enhanced Indexing Strategy
- **Full Documents**: Complete context preservation
- **Title Chunks**: Direct disease name matching
- **Symptoms Chunks**: Focused on clinical signs
- **Sentence Chunks**: Fine-grained search capability

### 2. Medical Query Expansion
- `lateral recumbency` → `lying on side`, `unable to stand`, `down cow`
- `curved neck` → `neck curvature`, `twisted neck`, `neck deviation`
- `hypocalcemia` → `milk fever`, `calcium deficiency`

### 3. Better Document Analysis
- Extract symptoms, diagnosis, treatment sections
- Identify key medical terms
- Create specialized metadata

### 4. Comprehensive Testing
- Test multiple query variations
- Analyze chunk type performance
- Compare old vs new results

## 📊 Expected Results

After running the improvements, you should see:

```
🔍 Expanded query: Lateral recumbency with curved neck → 
Lateral recumbency with curved neck lying on side unable to stand down cow neck curvature twisted neck

📋 Retrieved Document IDs and Details:
   1. Document: MILK FEVER.pdf
      Document ID: abc123-def456
      Similarity Score: 0.8542
      Text Preview: Symptoms and Clinical Signs of MILK FEVER: Lateral recumbency...
```

## 🛠️ Troubleshooting

### If MILK FEVER still not found:
1. Check document content: `python improve_search.py`
2. Verify indexing: Check if `documents_improved` table was created
3. Review analysis: Look at `analyze_documents.py` output

### If performance is slow:
1. Reduce chunk sizes in `improved_indexing.py`
2. Limit top_k values in searches
3. Consider using different embedding models

### If results are inconsistent:
1. Clear old indexes and reindex
2. Adjust similarity thresholds
3. Add more medical synonyms to query expansion

## 📈 Performance Comparison

The system now creates multiple document types per PDF:
- Original: 1 document per PDF
- Improved: ~5-10 documents per PDF (depending on content)

This increases search precision by allowing:
- Direct disease name matching
- Symptom-focused retrieval
- Context-aware chunking

## 🎯 Next Steps

1. Run the improvement pipeline
2. Test with your problematic query
3. If successful, use the improved system
4. If issues persist, check document content and consider alternative embedding models

## 📞 Support

If you encounter issues:
1. Check the output of each script for error messages
2. Verify database connectivity
3. Ensure all PDF files are readable
4. Check that the `diseases` folder contains the expected files
