import os
import fitz  # PyMuPDF
import psycopg2
from transformers import <PERSON>Tokenizer, AutoModel
import torch
import time
from datetime import datetime
import re
import json
from typing import List, Dict, Tuple

# --- CONFIG ---
DB_NAME = "kforce"
DB_USER = "postgres"
DB_PASS = "kforce"
PDF_FOLDER = "diseases"  # Folder containing PDFs

# --- Nomic Embedder ---
class NomicEmbedder:
    def __init__(self):
        self.tokenizer = AutoTokenizer.from_pretrained(
            "nomic-ai/nomic-embed-text-v1", trust_remote_code=True
        )
        self.model = AutoModel.from_pretrained(
            "nomic-ai/nomic-embed-text-v1", trust_remote_code=True
        )
        self.model.eval()

    def encode(self, texts):
        if isinstance(texts, str):
            texts = [texts]
        
        with torch.no_grad():
            inputs = self.tokenizer(texts, padding=True, truncation=True, 
                                  return_tensors="pt", max_length=512)
            outputs = self.model(**inputs)
            embeddings = outputs.last_hidden_state[:, 0, :]
            # Normalize embeddings for cosine similarity
            embeddings = torch.nn.functional.normalize(embeddings, p=2, dim=1)
            return embeddings.cpu().numpy().tolist()

# --- Enhanced PDF Text Extraction ---
def extract_text_from_pdf(file_path):
    doc = fitz.open(file_path)
    text_blocks = []
    
    for page_num, page in enumerate(doc):
        # Get text with position information
        blocks = page.get_text("dict")
        page_text = []
        
        for block in blocks["blocks"]:
            if "lines" in block:
                for line in block["lines"]:
                    line_text = ""
                    for span in line["spans"]:
                        line_text += span["text"]
                    if line_text.strip():
                        page_text.append(line_text.strip())
        
        if page_text:
            text_blocks.append({
                'page': page_num + 1,
                'content': '\n'.join(page_text)
            })
    
    doc.close()
    return text_blocks

# --- Structure-Aware Text Processing ---
def parse_structured_document(text: str) -> Dict:
    parsed_data = {
        'metadata': {},
        'sections': {},
        'raw_text': text
    }

    # Basic metadata extraction
    fields = {
        'complaint_id': r'Customer Complaint ID\s*(\S+)',
        'complaint_date': r'Date\s*([\d]{2} [A-Za-z]{3} [\d]{4})',
        'complaint_time': r'Time of Complaint\s*([\d:]+)',
        'farmer_name': r'Farmer Name\s*([^\n]+)',
        'farmer_phone': r'Farmer Phone No\s*(\+\d+)',
        'farmer_id': r'Farmer ID\s*(\S+)',
        'animal_name': r'Animal Name\s*([^\n]*)',
        'animal_tag': r'Animal Tag No\s*([^\n]+)',
        'animal_ref': r'Animal Reference Number\s*([^\n]+)',
        'village': r'Village\s*([^\n]+)',
        'officer': r'Veterinary Officer\s*([^\n]+)',
        'complaint': r'Activity / Complaint\s*([^\n]+)',
        'follow_up': r'Follow up Scheduled On\s*([^\n]+)',
    }

    for key, pattern in fields.items():
        match = re.search(pattern, text)
        if match:
            parsed_data['metadata'][key] = match.group(1).strip()

    # Diagnosis extraction
    diagnosis = re.findall(r'DIAGNOSIS\s+Sl No\.\s+Name\s+\d+\s+(.+)', text)
    parsed_data['sections']['diagnosis'] = '\n'.join(diagnosis) if diagnosis else ''

    # Extract Prescription Table (with price, unit, frequency, etc.)
    medicine_pattern = re.findall(
        r'\d+\s+([A-Z0-9 \(\)-]+)\s+([\d.]+)\s+([A-Z]+)\s+([\d.]+)\s+([A-Z]+)\s+([A-Z]+)\s+([A-Z]+)\s+₹([\d.]+)',
        text
    )
    parsed_data['sections']['medicines'] = medicine_pattern

    # Vet Advisory Notes
    if 'vet_advisory' in parsed_data['sections']:
        advisory = parsed_data['sections']['vet_advisory']
        if advisory:
            chunks.append({
                'id': f"{source_file}_{chunk_id}",
                'content': f"Veterinary Advisory:\n{advisory}",
                'chunk_type': 'advisory',
                'source': source_file,
                'importance': 'medium',
                'medical_relations': {}
            })
            chunk_id += 1


    return parsed_data

# --- Smart Chunking Strategy ---
def create_smart_chunks(parsed_data: Dict, source_file: str) -> List[Dict]:
    """Create intelligent chunks based on document structure with enhanced medical relationships"""
    chunks = []
    chunk_id = 0
    
    # Extract medical relationships with improved pattern matching
    medical_relations = extract_diagnosis_medication_relations(parsed_data)
    
    # 1. Metadata chunk - Important identifiers
    metadata_text = []
    for key, value in parsed_data['metadata'].items():
        metadata_text.append(f"{key.replace('_', ' ').title()}: {value}")
    
    if metadata_text:
        chunks.append({
            'id': f"{source_file}_{chunk_id}",
            'content': "Document Metadata:\n" + "\n".join(metadata_text),
            'chunk_type': 'metadata',
            'source': source_file,
            'importance': 'high',
            'medical_relations': {}  # No medical relations in metadata
        })
        chunk_id += 1
    
    # 2. Section-based chunks with enhanced medical relation mapping
    for section_name, section_content in parsed_data['sections'].items():
        if section_content and len(section_content.strip()) > 10:
            # Add medical relations if this is a diagnosis or treatment section
            section_relations = {}
            if section_name == 'diagnosis':
                section_relations = {'diagnosis_to_medication': medical_relations['diagnosis_to_medication']}
            elif section_name in ['treatment', 'prescription']:
                section_relations = {'medication_to_diagnosis': medical_relations['medication_to_diagnosis']}
            
            chunks.append({
                'id': f"{source_file}_{chunk_id}",
                'content': f"{section_name.title()} Section:\n{section_content}",
                'chunk_type': 'section',
                'section': section_name,
                'source': source_file,
                'importance': 'medium' if section_name in ['history', 'treatment'] else 'low',
                'medical_relations': section_relations
            })
            chunk_id += 1
    
    # 3. Medicine/Treatment detailed chunk with enhanced relationship data
    if 'medicines' in parsed_data['sections']:
        medicine_text = "Prescribed Medicines and Treatment:\n"
        for med in parsed_data['sections']['medicines']:
            if isinstance(med, (list, tuple)) and len(med) >= 7:
                medicine_text += f"Medicine: {med[1]}, Dose: {med[2]} {med[3]}, Route: {med[6]}\n"
                
                # Add related diagnoses if available
                med_name = med[1].strip().lower()
                if med_name in medical_relations['medication_to_diagnosis']:
                    related_diagnoses = medical_relations['medication_to_diagnosis'][med_name].get('diagnoses', [])
                    if related_diagnoses:
                        medicine_text += f"Related Diagnoses: {', '.join(related_diagnoses)}\n"
        
        chunks.append({
            'id': f"{source_file}_{chunk_id}",
            'content': medicine_text,
            'chunk_type': 'treatment',
            'source': source_file,
            'importance': 'high',
            'medical_relations': {'medication_to_diagnosis': medical_relations['medication_to_diagnosis']}
        })
        chunk_id += 1
    
    # 4. Dedicated medical relationships chunk with enhanced formatting
    if medical_relations['diagnosis_to_medication'] or medical_relations['medication_to_diagnosis']:
        relations_text = "Medical Relationships:\n\n"
        
        # Add diagnosis to medication relationships with confidence scores
        if medical_relations['diagnosis_to_medication']:
            relations_text += "DIAGNOSIS TO MEDICATION RELATIONSHIPS:\n"
            for diagnosis, data in medical_relations['diagnosis_to_medication'].items():
                medications = data.get('medications', [])
                confidence = data.get('confidence', 0)
                if medications:
                    relations_text += f"• Diagnosis: {diagnosis}\n"
                    relations_text += f"  → Medications: {', '.join(medications)}\n"
                    relations_text += f"  → Confidence: {confidence:.2f}\n\n"
        
        # Add medication to diagnosis relationships with confidence scores
        if medical_relations['medication_to_diagnosis']:
            relations_text += "MEDICATION TO DIAGNOSIS RELATIONSHIPS:\n"
            for medication, data in medical_relations['medication_to_diagnosis'].items():
                diagnoses = data.get('diagnoses', [])
                confidence = data.get('confidence', 0)
                if diagnoses:
                    relations_text += f"• Medication: {medication}\n"
                    relations_text += f"  → Diagnoses: {', '.join(diagnoses)}\n"
                    relations_text += f"  → Confidence: {confidence:.2f}\n\n"
        
        chunks.append({
            'id': f"{source_file}_{chunk_id}",
            'content': relations_text,
            'chunk_type': 'medical_relations',
            'source': source_file,
            'importance': 'high',
            'medical_relations': medical_relations
        })
        chunk_id += 1
    
    # 5. Full context chunk with summary of medical relationships
    full_context = f"Complete Document Context:\n"
    full_context += f"Source: {source_file}\n"
    
    if parsed_data['metadata']:
        full_context += "Key Information: " + ", ".join([f"{k}: {v}" for k, v in parsed_data['metadata'].items()]) + "\n"
    
    # Add summary of medical relationships
    diagnosis_count = len(medical_relations['diagnosis_to_medication'])
    medication_count = len(medical_relations['medication_to_diagnosis'])
    if diagnosis_count > 0 or medication_count > 0:
        full_context += f"Medical Relationships: {diagnosis_count} diagnoses, {medication_count} medications\n"
    
    full_context += f"Full Text: {parsed_data['raw_text'][:1000]}..."  # Truncate if too long
    
    chunks.append({
        'id': f"{source_file}_{chunk_id}",
        'content': full_context,
        'chunk_type': 'full_context',
        'source': source_file,
        'importance': 'medium',
        'medical_relations': medical_relations
    })
    
    return chunks

# --- Enhanced Database Storage ---
def store_embeddings_enhanced(chunks: List[Dict], embedder):
    """Store chunks with enhanced metadata for better retrieval"""
    contents = [chunk['content'] for chunk in chunks]
    vectors = embedder.encode(contents)
    
    conn = psycopg2.connect(dbname=DB_NAME, user=DB_USER, password=DB_PASS, 
                           host="localhost", port=5444)
    cur = conn.cursor()
    
    # Create enhanced table if not exists
    cur.execute("""
    CREATE TABLE IF NOT EXISTS documents_enhanced (
        id SERIAL PRIMARY KEY,
        chunk_id VARCHAR(255) UNIQUE,
        content TEXT,
        embedding vector(768),
        source VARCHAR(255),
        chunk_type VARCHAR(50),
        section VARCHAR(100),
        importance VARCHAR(20),
        metadata JSONB,
        medical_relations JSONB,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    """)
    
    for chunk, vec in zip(chunks, vectors):
        try:
            # Convert medical relations to JSON string
            medical_relations_json = json.dumps(chunk.get('medical_relations', {}))
            
            cur.execute("""
            INSERT INTO documents_enhanced (chunk_id, content, embedding, source, 
                                          chunk_type, section, importance, metadata,
                                          medical_relations) 
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            ON CONFLICT (chunk_id) DO UPDATE SET
                content = EXCLUDED.content,
                embedding = EXCLUDED.embedding,
                metadata = EXCLUDED.metadata,
                medical_relations = EXCLUDED.medical_relations
            """, (
                chunk['id'],
                chunk['content'], 
                vec, 
                chunk['source'],
                chunk['chunk_type'],
                chunk.get('section', ''),
                chunk['importance'],
                json.dumps({k: v for k, v in chunk.items() if k not in ['id', 'content', 'source', 'chunk_type', 'section', 'importance', 'medical_relations']}),
                medical_relations_json
            ))
        except Exception as e:
            print(f"Error inserting chunk {chunk['id']}: {e}")
            continue
    
    conn.commit()
    cur.close()
    conn.close()

# --- Enhanced Medical Relationship Extraction ---
def extract_diagnosis_medication_relations(parsed_data: Dict) -> Dict:
    """Extract relationships between diagnoses and medications with improved pattern matching"""
    relations = {
        'diagnosis_to_medication': {},
        'medication_to_diagnosis': {}
    }
    
    # Extract diagnoses from sections with more comprehensive patterns
    diagnoses = []
    if 'diagnosis' in parsed_data['sections']:
        diagnosis_text = parsed_data['sections']['diagnosis']
        # Enhanced diagnosis patterns
        diagnosis_patterns = [
            r'([A-Za-z\s]+(?:infection|disease|disorder|condition|syndrome|injury|itis))',
            r'diagnosed with\s+([^,.;]+)',
            r'diagnosis[:\s]+([^,.;]+)',
            r'assessment[:\s]+([^,.;]+)',
            r'impression[:\s]+([^,.;]+)',
            r'suspected\s+([^,.;]+\b(?:infection|disease|disorder|condition))'
        ]
        for pattern in diagnosis_patterns:
            matches = re.findall(pattern, diagnosis_text, re.IGNORECASE)
            diagnoses.extend([match.strip() for match in matches])
    
    # Also check history section for diagnoses
    if 'history' in parsed_data['sections']:
        history_text = parsed_data['sections']['history']
        history_diagnosis_patterns = [
            r'history of\s+([^,.;]+\b(?:infection|disease|disorder|condition))',
            r'presented with\s+([^,.;]+)',
            r'showing signs of\s+([^,.;]+)'
        ]
        for pattern in history_diagnosis_patterns:
            matches = re.findall(pattern, history_text, re.IGNORECASE)
            diagnoses.extend([match.strip() for match in matches])
    
    # Extract medications with improved detection
    medications = []
    
    # First check structured medicines section
    if 'medicines' in parsed_data['sections']:
        for med in parsed_data['sections']['medicines']:
            if isinstance(med, (list, tuple)) and len(med) > 1:
                medications.append(med[1].strip())  # Medicine name is in position 1
    
    # Check treatment/prescription sections
    treatment_sections = ['treatment', 'prescription']
    for section_name in treatment_sections:
        if section_name in parsed_data['sections']:
            section_text = parsed_data['sections'][section_name]
            medication_patterns = [
                r'([A-Za-z\s]+)\s+(\d+\s*(?:mg|ml|g|mcg|IU))',
                r'administered\s+([A-Za-z\s]+)',
                r'given\s+([A-Za-z\s]+)',
                r'prescribed\s+([A-Za-z\s]+)',
                r'([A-Za-z\s]+)\s+(?:IV|IM|SC|PO|SID|BID|TID|QID)',
                r'([A-Za-z\s]+)\s+(?:tablet|capsule|injection|solution)'
            ]
            for pattern in medication_patterns:
                matches = re.findall(pattern, section_text, re.IGNORECASE)
                if matches:
                    for match in matches:
                        if isinstance(match, tuple):
                            medications.append(match[0].strip())
                        else:
                            medications.append(match.strip())
    
    # Remove duplicates and clean up
    diagnoses = list(set([d.lower() for d in diagnoses if len(d) > 3]))
    medications = list(set([m.lower() for m in medications if len(m) > 3]))
    
    # Filter out common false positives
    stop_words = {'the', 'and', 'with', 'for', 'this', 'that', 'these', 'those', 
                 'patient', 'animal', 'treatment', 'medication', 'diagnosis'}
    diagnoses = [d for d in diagnoses if d.lower() not in stop_words]
    medications = [m for m in medications if m.lower() not in stop_words]
    
    # Create bidirectional relationships with confidence scores
    for diagnosis in diagnoses:
        relations['diagnosis_to_medication'][diagnosis] = {
            'medications': medications,
            'confidence': 0.8 if len(medications) > 0 else 0.5
        }
    
    for medication in medications:
        relations['medication_to_diagnosis'][medication] = {
            'diagnoses': diagnoses,
            'confidence': 0.8 if len(diagnoses) > 0 else 0.5
        }
    
    return relations

# --- MAIN TRAINING PIPELINE ---
if __name__ == "__main__":
    # Create pdf folder if it doesn't exist
    if not os.path.exists(PDF_FOLDER):
        os.makedirs(PDF_FOLDER)
        print(f"Created '{PDF_FOLDER}' folder. Please add PDF files to this folder.")
        exit()
    
    # Get list of PDF files
    pdf_files = [f for f in os.listdir(PDF_FOLDER) if f.lower().endswith('.pdf')]
    
    if not pdf_files:
        print(f"No PDF files found in '{PDF_FOLDER}' folder. Please add some PDF files.")
        exit()
    
    # Initialize embedder
    embedder = NomicEmbedder()
    start_time = time.time()
    start_datetime = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"Starting enhanced embedding processing at: {start_datetime}")
    
    total_chunks = 0
    
    # Process each PDF
    for i, pdf_file in enumerate(pdf_files, 1):
        file_path = os.path.join(PDF_FOLDER, pdf_file)
        print(f"Processing {i}/{len(pdf_files)}: {pdf_file}")
        
        try:
            # Extract text with structure awareness
            text_blocks = extract_text_from_pdf(file_path)
            
            all_chunks = []
            for block in text_blocks:
                # Parse structured content
                parsed_data = parse_structured_document(block['content'])
                
                # Create smart chunks
                chunks = create_smart_chunks(parsed_data, pdf_file)
                all_chunks.extend(chunks)
            
            # Store embeddings with enhanced metadata
            if all_chunks:
                store_embeddings_enhanced(all_chunks, embedder)
                total_chunks += len(all_chunks)
                
                current_datetime = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                print(f"✓ Completed {pdf_file}: {len(all_chunks)} chunks stored at {current_datetime}")
            else:
                print(f"⚠ No chunks created for {pdf_file}")
                
        except Exception as e:
            print(f"✗ Error processing {pdf_file}: {e}")
            continue
    
    end_time = time.time()
    end_datetime = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"\n=== Processing Complete ===")
    print(f"Finished at: {end_datetime}")
    print(f"Total time: {end_time - start_time:.2f} seconds")
    print(f"Total files processed: {len(pdf_files)}")
    print(f"Total chunks created: {total_chunks}")
    print(f"Average chunks per file: {total_chunks/len(pdf_files):.1f}")
