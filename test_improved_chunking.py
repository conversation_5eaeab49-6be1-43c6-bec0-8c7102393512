#!/usr/bin/env python3
"""
Test the improved chunking strategy vs old strategy
"""

import os
from pathlib import Path
from dotenv import load_dotenv
from pdf_embedder import PDFProcessor

# Load environment variables
load_dotenv()

def test_chunking_comparison():
    """Compare old vs new chunking strategies"""
    
    print("🧪 Testing Improved Chunking Strategy")
    print("=" * 60)
    
    # Get training data path
    training_folder = os.getenv("TRAINING_DATA_PATH", "diseases")
    
    # Test with a few sample PDFs
    test_pdfs = [
        "ACIDOSIS.pdf",
        "PNEUMONIA.pdf", 
        "ANEMIA.pdf",
        "EYE INFECTION.pdf"
    ]
    
    processor = PDFProcessor()
    
    for pdf_name in test_pdfs:
        pdf_path = Path(training_folder) / pdf_name
        
        if not pdf_path.exists():
            print(f"❌ {pdf_name} not found, skipping...")
            continue
        
        print(f"\n📄 Testing {pdf_name}")
        print("-" * 40)
        
        # Extract text
        text = processor.extract_text_from_pdf(str(pdf_path))
        if not text:
            print(f"❌ No text extracted from {pdf_name}")
            continue
        
        print(f"📝 Total text length: {len(text)} characters")
        print(f"📝 Total words: {len(text.split())} words")
        
        # Test new chunking strategy
        chunks = processor.chunk_text(text, pdf_name)
        
        print(f"🔧 New Strategy Results:")
        print(f"   📊 Created {len(chunks)} chunks")
        
        # Show chunk details
        total_words = 0
        for i, chunk in enumerate(chunks, 1):
            heading = chunk.get('heading', 'Unknown')
            word_count = chunk.get('word_count', 0)
            total_words += word_count
            
            print(f"   {i:2d}. {heading} ({word_count} words)")
            
            # Show first 100 chars of content
            content_preview = chunk['text'][:100].replace('\n', ' ')
            print(f"       Preview: {content_preview}...")
        
        print(f"   📈 Total words in chunks: {total_words}")
        print(f"   📊 Average chunk size: {total_words / len(chunks):.1f} words")
        
        # Calculate coverage
        coverage = (total_words / len(text.split())) * 100
        print(f"   📋 Text coverage: {coverage:.1f}%")

def analyze_chunk_quality():
    """Analyze the quality of chunks created"""
    
    print(f"\n🔍 Chunk Quality Analysis")
    print("=" * 40)
    
    training_folder = os.getenv("TRAINING_DATA_PATH", "diseases")
    processor = PDFProcessor()
    
    # Test with ACIDOSIS.pdf for detailed analysis
    pdf_path = Path(training_folder) / "ACIDOSIS.pdf"
    
    if not pdf_path.exists():
        print("❌ ACIDOSIS.pdf not found for detailed analysis")
        return
    
    text = processor.extract_text_from_pdf(str(pdf_path))
    chunks = processor.chunk_text(text, "ACIDOSIS.pdf")
    
    print(f"📄 Detailed Analysis of ACIDOSIS.pdf")
    print(f"Created {len(chunks)} chunks:")
    
    for i, chunk in enumerate(chunks, 1):
        print(f"\n--- Chunk {i}: {chunk['heading']} ---")
        print(f"Word count: {chunk['word_count']}")
        print(f"Content:")
        print(chunk['text'])
        print("-" * 50)

def test_medical_section_detection():
    """Test how well the system detects medical sections"""
    
    print(f"\n🏥 Medical Section Detection Test")
    print("=" * 40)
    
    training_folder = os.getenv("TRAINING_DATA_PATH", "diseases")
    processor = PDFProcessor()
    
    # Test with multiple PDFs
    test_pdfs = ["ACIDOSIS.pdf", "PNEUMONIA.pdf", "ANEMIA.pdf"]
    
    medical_sections_found = []
    
    for pdf_name in test_pdfs:
        pdf_path = Path(training_folder) / pdf_name
        
        if not pdf_path.exists():
            continue
        
        text = processor.extract_text_from_pdf(str(pdf_path))
        chunks = processor.chunk_text(text, pdf_name)
        
        print(f"\n📄 {pdf_name}:")
        for chunk in chunks:
            heading = chunk['heading'].lower()
            
            # Check if heading matches medical sections
            medical_keywords = ['symptom', 'treatment', 'diagnosis', 'occurrence', 'prevention', 'cause']
            is_medical = any(keyword in heading for keyword in medical_keywords)
            
            if is_medical:
                medical_sections_found.append(heading)
                print(f"   ✅ Medical section: {chunk['heading']}")
            else:
                print(f"   📝 General section: {chunk['heading']}")
    
    print(f"\n📊 Summary:")
    print(f"Medical sections found: {len(set(medical_sections_found))}")
    print(f"Unique medical sections: {list(set(medical_sections_found))}")

def main():
    """Main test function"""
    
    # Test chunking comparison
    test_chunking_comparison()
    
    # Analyze chunk quality
    analyze_chunk_quality()
    
    # Test medical section detection
    test_medical_section_detection()
    
    print(f"\n✅ Improved Chunking Strategy Testing Complete!")
    print(f"\n💡 Key Improvements:")
    print(f"   • Adaptive chunk sizes based on content type")
    print(f"   • Better medical section detection")
    print(f"   • Smarter break points (sentences, lists)")
    print(f"   • Content-aware chunking")
    print(f"   • Preserved document structure")

if __name__ == "__main__":
    main()
