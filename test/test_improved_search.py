#!/usr/bin/env python3
"""
Test the improved search system with enhanced indexing
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the RAGSystem class from main.llamaindex.py
exec(open('main.llamaindex.py').read())

def test_improved_search():
    """Test the improved search system"""
    
    print("🧪 Testing Improved Search System")
    print("=" * 60)
    
    try:
        # Initialize RAG system (now using improved index)
        print("🔧 Initializing improved RAG system...")
        rag = RAGSystem()
        
        # Test the problematic query
        problematic_query = "Lateral recumbency with curved neck on lateral side of body"
        
        print(f"\n🎯 PRIMARY TEST - Problematic Query:")
        print(f"Query: '{problematic_query}'")
        print(f"Expected: MILK FEVER should be in top results")
        print("-" * 60)
        
        # Test with different top_k values
        for top_k in [3, 5, 10]:
            print(f"\n📊 Testing with top_k = {top_k}:")
            results = rag.retrieve_only(problematic_query, top_k=top_k)
            
            if results:
                milk_fever_found = False
                milk_fever_rank = None
                
                for i, result in enumerate(results, 1):
                    source = result.get('source', '').upper()
                    chunk_type = result.get('chunk_type', 'unknown')
                    section_type = result.get('section_type', 'unknown')
                    score = result.get('score', 0.0)
                    
                    if 'MILK FEVER' in source:
                        milk_fever_found = True
                        milk_fever_rank = i
                        print(f"   🎯 RANK {i}: MILK FEVER found!")
                        print(f"      Source: {result.get('source', 'Unknown')}")
                        print(f"      Score: {score:.4f}")
                        print(f"      Chunk Type: {chunk_type}")
                        print(f"      Section Type: {section_type}")
                        print(f"      Preview: {result.get('text_preview', 'No preview')[:100]}...")
                        break
                
                if not milk_fever_found:
                    print(f"   ❌ MILK FEVER not found in top {top_k}")
                    print(f"   📄 Top result: {results[0].get('source', 'Unknown')} (Score: {results[0].get('score', 0.0):.4f})")
                else:
                    print(f"   ✅ MILK FEVER found at rank {milk_fever_rank}")
            else:
                print(f"   ❌ No results returned")
        
        # Test different query variations
        print(f"\n🔍 TESTING QUERY VARIATIONS:")
        print("-" * 60)
        
        query_variations = [
            "milk fever symptoms",
            "hypocalcemia cattle",
            "cow lying down unable to stand",
            "lateral recumbency",
            "curved neck cattle",
            "calcium deficiency symptoms",
            "postpartum paralysis",
            "recumbency curved neck"
        ]
        
        variation_results = {}
        
        for query in query_variations:
            print(f"\n🔎 Query: '{query}'")
            results = rag.retrieve_only(query, top_k=3)
            
            if results:
                milk_fever_rank = None
                for i, result in enumerate(results, 1):
                    source = result.get('source', '').upper()
                    if 'MILK FEVER' in source:
                        milk_fever_rank = i
                        score = result.get('score', 0.0)
                        chunk_type = result.get('chunk_type', 'unknown')
                        print(f"   ✅ MILK FEVER at rank {i} (Score: {score:.4f}, Type: {chunk_type})")
                        break
                
                if milk_fever_rank is None:
                    print(f"   ❌ MILK FEVER not in top 3")
                    print(f"   📄 Top: {results[0].get('source', 'Unknown')} (Score: {results[0].get('score', 0.0):.4f})")
                
                variation_results[query] = milk_fever_rank
            else:
                print(f"   ❌ No results")
                variation_results[query] = None
        
        # Analyze chunk types performance
        print(f"\n📊 CHUNK TYPE ANALYSIS:")
        print("-" * 60)
        
        # Get detailed results for analysis
        detailed_results = rag.retrieve_only(problematic_query, top_k=15)
        
        if detailed_results:
            chunk_type_performance = {}
            
            for result in detailed_results:
                source = result.get('source', '')
                chunk_type = result.get('chunk_type', 'unknown')
                section_type = result.get('section_type', 'unknown')
                score = result.get('score', 0.0)
                
                if 'MILK FEVER' in source.upper():
                    key = f"{chunk_type}_{section_type}"
                    if key not in chunk_type_performance:
                        chunk_type_performance[key] = []
                    chunk_type_performance[key].append(score)
            
            if chunk_type_performance:
                print(f"🎯 MILK FEVER chunk types found:")
                for chunk_key, scores in sorted(chunk_type_performance.items(), 
                                              key=lambda x: max(x[1]), reverse=True):
                    avg_score = sum(scores) / len(scores)
                    max_score = max(scores)
                    print(f"   {chunk_key}: {len(scores)} chunks, avg score: {avg_score:.4f}, max: {max_score:.4f}")
            else:
                print(f"❌ No MILK FEVER chunks found in top 15 results")
        
        # Summary and recommendations
        print(f"\n📈 SUMMARY:")
        print("-" * 60)
        
        successful_variations = [q for q, rank in variation_results.items() if rank is not None]
        
        print(f"✅ Successful query variations: {len(successful_variations)}/{len(query_variations)}")
        print(f"📊 Success rate: {len(successful_variations)/len(query_variations)*100:.1f}%")
        
        if successful_variations:
            print(f"\n🎯 Best performing queries:")
            for query, rank in sorted(variation_results.items(), key=lambda x: x[1] or 999):
                if rank is not None:
                    print(f"   Rank {rank}: '{query}'")
        
        # Test full RAG pipeline
        print(f"\n🤖 TESTING FULL RAG PIPELINE:")
        print("-" * 60)
        
        print(f"Query: '{problematic_query}'")
        response = rag.query(problematic_query, use_ollama_direct=True, verbose=True)
        
        # Check if MILK FEVER is mentioned in sources
        sources = response.get('sources', [])
        milk_fever_in_sources = any('MILK FEVER' in source.get('source', '').upper() for source in sources)
        
        if milk_fever_in_sources:
            print(f"\n✅ SUCCESS: MILK FEVER found in RAG response sources!")
        else:
            print(f"\n❌ ISSUE: MILK FEVER not found in RAG response sources")
        
        return variation_results
        
    except Exception as e:
        print(f"❌ Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()
        return {}

if __name__ == "__main__":
    results = test_improved_search()
    
    print(f"\n🎯 NEXT STEPS:")
    print("=" * 60)
    print("1. If results improved: Great! The enhanced indexing is working")
    print("2. If still having issues: Run 'python analyze_documents.py' to check document content")
    print("3. Consider adjusting chunk types or similarity thresholds")
    print("4. Test with different embedding models if needed")
