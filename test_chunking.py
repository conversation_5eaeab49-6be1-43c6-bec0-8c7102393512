#!/usr/bin/env python3
"""
Test the new heading-based chunking system
"""

import os
from dotenv import load_dotenv
from pdf_embedder import PDFProcessor
import fitz

# Load environment variables
load_dotenv()

def test_pdf_content(pdf_path):
    """Test PDF content extraction and chunking"""
    
    print(f"📄 Testing PDF: {pdf_path}")
    print("=" * 50)
    
    # Extract raw text
    try:
        doc = fitz.open(pdf_path)
        raw_text = ""
        for page_num in range(doc.page_count):
            page = doc[page_num]
            raw_text += page.get_text()
        doc.close()
        
        print(f"📊 Raw text length: {len(raw_text)} characters")
        print(f"📊 Raw text preview:")
        print("-" * 30)
        print(raw_text[:500] + "..." if len(raw_text) > 500 else raw_text)
        print("-" * 30)
        
    except Exception as e:
        print(f"❌ Error extracting text: {e}")
        return
    
    # Test chunking
    processor = PDFProcessor()
    chunks = processor.chunk_text(raw_text, pdf_path)
    
    print(f"\n📋 Chunking Results:")
    print(f"   Total chunks: {len(chunks)}")
    
    for i, chunk in enumerate(chunks):
        print(f"\n   Chunk {i+1}:")
        print(f"     Heading: {chunk.get('heading', 'N/A')}")
        print(f"     Word count: {chunk['word_count']}")
        print(f"     Content preview: {chunk['text'][:200]}...")
        print(f"     Chunk ID: {chunk['chunk_id'][:16]}...")

def main():
    """Test chunking with a few sample PDFs"""

    # Get training data path from environment
    training_folder = os.getenv("TRAINING_DATA_PATH", "diseases")

    test_pdfs = [
        f"{training_folder}/ACIDOSIS.pdf",
        f"{training_folder}/PNEUMONIA.pdf",
        f"{training_folder}/ANEMIA.pdf"
    ]
    
    for pdf_path in test_pdfs:
        try:
            test_pdf_content(pdf_path)
            print("\n" + "=" * 80 + "\n")
        except Exception as e:
            print(f"❌ Error testing {pdf_path}: {e}")

if __name__ == "__main__":
    main()
