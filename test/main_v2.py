import fitz  # PyMuPDF
import psycopg2
from transformers import <PERSON>Tokenizer, AutoModel
import torch
from ollama import chat
import time
from datetime import datetime
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity
import re
from collections import Counter

# --- CONFIG ---
DB_NAME = "kforce"
DB_USER = "postgres"
DB_PASS = "kforce"
OLLAMA_MODEL = "llama3"

# --- Nomic Embedder ---
class NomicEmbedder:
    def __init__(self):
        self.tokenizer = AutoTokenizer.from_pretrained(
            "nomic-ai/nomic-embed-text-v1", trust_remote_code=True
        )
        self.model = AutoModel.from_pretrained(
            "nomic-ai/nomic-embed-text-v1", trust_remote_code=True
        )
        self.model.eval()

    def encode(self, texts):
        with torch.no_grad():
            inputs = self.tokenizer(texts, padding=True, truncation=True, return_tensors="pt")
            outputs = self.model(**inputs)
            embeddings = outputs.last_hidden_state[:, 0, :]
            return embeddings.cpu().numpy()

# --- Occurrence Search Functions ---
def extract_keywords(text, min_length=3):
    """Extract meaningful keywords from text"""
    text = re.sub(r'[^\w\s]', ' ', text.lower())
    words = text.split()
    
    stop_words = {'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 
                  'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 
                  'did', 'will', 'would', 'could', 'should', 'may', 'might', 'must', 'shall', 
                  'can', 'this', 'that', 'these', 'those', 'a', 'an'}
    
    keywords = [word for word in words if len(word) >= min_length and word not in stop_words]
    return keywords

def calculate_occurrence_score(content, query_keywords):
    """Calculate occurrence score based on keyword frequency"""
    content_lower = content.lower()
    total_score = 0
    
    for keyword in query_keywords:
        count = content_lower.count(keyword.lower())
        total_score += count
    
    return total_score

def cosine_similarity_score(vec1, vec2):
    """Calculate cosine similarity between two vectors"""
    vec1 = np.array(vec1).reshape(1, -1)
    vec2 = np.array(vec2).reshape(1, -1)
    return cosine_similarity(vec1, vec2)[0][0]

# --- OPTIMIZED: Database-Level Vector Similarity ---
def query_similar_chunks_pgvector(question, embedder, top_k=10):
    """Use pgvector for efficient similarity search at database level"""
    query_embedding = embedder.encode([question])[0]
    
    conn = psycopg2.connect(
        dbname=DB_NAME, 
        user=DB_USER, 
        password=DB_PASS,
        host="localhost",
        port=5444
    )
    cur = conn.cursor()
    
    # Use pgvector's cosine distance operator for efficient search
    cur.execute("""
        SELECT content, embedding <=> %s::vector as distance
        FROM documents 
        ORDER BY embedding <=> %s::vector
        LIMIT %s
    """, (query_embedding.tolist(), query_embedding.tolist(), top_k))
    
    results = cur.fetchall()
    cur.close()
    conn.close()
    
    return [r[0] for r in results]

# --- OPTIMIZED: Hybrid Search with Pre-filtering ---
def query_similar_chunks_hybrid_optimized(question, embedder, top_k=10, occurrence_weight=0.3, similarity_weight=0.7, prefilter_size=100):
    """
    Optimized hybrid search with database-level pre-filtering
    
    Args:
        question: User's question
        embedder: Embedding model
        top_k: Number of top results to return
        occurrence_weight: Weight for occurrence score (0-1)
        similarity_weight: Weight for cosine similarity score (0-1)
        prefilter_size: Number of candidates to get from database before hybrid scoring
    """
    query_embedding = embedder.encode([question])[0]
    query_keywords = extract_keywords(question)
    
    conn = psycopg2.connect(
        dbname=DB_NAME, 
        user=DB_USER, 
        password=DB_PASS,
        host="localhost",
        port=5444
    )
    cur = conn.cursor()
    
    # STEP 1: Use database-level vector similarity to get top candidates
    # This is much faster than loading all embeddings
    cur.execute("""
        SELECT id, content, embedding, embedding <=> %s::vector as distance
        FROM documents 
        ORDER BY embedding <=> %s::vector
        LIMIT %s
    """, (query_embedding.tolist(), query_embedding.tolist(), prefilter_size))
    
    candidates = cur.fetchall()
    cur.close()
    conn.close()
    
    # STEP 2: Apply hybrid scoring only to pre-filtered candidates
    scored_docs = []
    
    for doc_id, content, embedding_data, distance in candidates:
        try:
            # Convert distance to cosine similarity (pgvector uses distance)
            cosine_score = 1 - distance  # Distance to similarity conversion
            
            # Calculate occurrence score
            occurrence_score = calculate_occurrence_score(content, query_keywords)
            
            # Normalize occurrence score
            max_possible_occurrences = len(query_keywords) * 5  # More conservative estimate
            normalized_occurrence_score = min(occurrence_score / max_possible_occurrences, 1.0) if max_possible_occurrences > 0 else 0
            
            # Calculate hybrid score
            hybrid_score = (similarity_weight * cosine_score) + (occurrence_weight * normalized_occurrence_score)
            
            scored_docs.append({
                'id': doc_id,
                'content': content,
                'cosine_score': cosine_score,
                'occurrence_score': occurrence_score,
                'hybrid_score': hybrid_score
            })
            
        except Exception as e:
            print(f"Error processing document {doc_id}: {e}")
            continue
    
    # Sort by hybrid score and return top_k
    scored_docs.sort(key=lambda x: x['hybrid_score'], reverse=True)
    
    # Print scoring details for debugging
    print(f"\nQuery keywords: {query_keywords}")
    print(f"Pre-filtered {len(candidates)} candidates from database")
    print(f"Top {min(5, len(scored_docs))} scoring documents:")
    for i, doc in enumerate(scored_docs[:5]):
        print(f"  {i+1}. Hybrid: {doc['hybrid_score']:.3f} (Cosine: {doc['cosine_score']:.3f}, Occurrence: {doc['occurrence_score']})")
    
    return [doc['content'] for doc in scored_docs[:top_k]]

# --- OPTIMIZED: Keyword-First Hybrid Search ---
def query_similar_chunks_keyword_first(question, embedder, top_k=10, occurrence_weight=0.4, similarity_weight=0.6):
    """
    Keyword-first hybrid search: Use PostgreSQL full-text search + vector similarity
    """
    query_embedding = embedder.encode([question])[0]
    query_keywords = extract_keywords(question)
    
    # Create search query for PostgreSQL full-text search
    search_query = ' | '.join(query_keywords) if query_keywords else question
    
    conn = psycopg2.connect(
        dbname=DB_NAME, 
        user=DB_USER, 
        password=DB_PASS,
        host="localhost",
        port=5444
    )
    cur = conn.cursor()
    
    # Use PostgreSQL's full-text search combined with vector similarity
    cur.execute("""
        SELECT 
            id, 
            content, 
            embedding,
            ts_rank(to_tsvector('english', content), to_tsquery('english', %s)) as text_rank,
            embedding <=> %s::vector as distance
        FROM documents 
        WHERE to_tsvector('english', content) @@ to_tsquery('english', %s)
           OR embedding <=> %s::vector < 0.7  -- Include semantically similar docs
        ORDER BY (
            %s * ts_rank(to_tsvector('english', content), to_tsquery('english', %s)) +
            %s * (1 - (embedding <=> %s::vector))
        ) DESC
        LIMIT %s
    """, (
        search_query, query_embedding.tolist(), search_query, query_embedding.tolist(),
        occurrence_weight, search_query, similarity_weight, query_embedding.tolist(), top_k * 2
    ))
    
    results = cur.fetchall()
    cur.close()
    conn.close()
    
    print(f"\nFound {len(results)} results using keyword-first search")
    print(f"Query keywords: {query_keywords}")
    
    return [r[1] for r in results[:top_k]]  # Return content only

# --- Memory-Efficient Batch Processing ---
def query_similar_chunks_batch_optimized(question, embedder, top_k=10, batch_size=1000):
    """
    Process documents in batches to avoid memory issues
    """
    query_embedding = embedder.encode([question])[0]
    query_keywords = extract_keywords(question)
    
    conn = psycopg2.connect(
        dbname=DB_NAME, 
        user=DB_USER, 
        password=DB_PASS,
        host="localhost",
        port=5444
    )
    cur = conn.cursor()
    
    # Get total document count
    cur.execute("SELECT COUNT(*) FROM documents")
    total_docs = cur.fetchone()[0]
    
    print(f"Processing {total_docs} documents in batches of {batch_size}")
    
    all_scored_docs = []
    
    # Process in batches
    for offset in range(0, total_docs, batch_size):
        cur.execute("""
            SELECT id, content, embedding 
            FROM documents 
            ORDER BY id 
            LIMIT %s OFFSET %s
        """, (batch_size, offset))
        
        batch_docs = cur.fetchall()
        print(f"Processing batch {offset//batch_size + 1}/{(total_docs + batch_size - 1)//batch_size}")
        
        for doc_id, content, embedding_data in batch_docs:
            try:
                if isinstance(embedding_data, str):
                    embedding = np.fromstring(embedding_data.strip('[]'), sep=',')
                else:
                    embedding = np.array(embedding_data)
                
                cosine_score = cosine_similarity_score(query_embedding, embedding)
                occurrence_score = calculate_occurrence_score(content, query_keywords)
                
                # Simple hybrid scoring
                hybrid_score = 0.7 * cosine_score + 0.3 * min(occurrence_score / 10, 1.0)
                
                all_scored_docs.append({
                    'content': content,
                    'hybrid_score': hybrid_score
                })
                
            except Exception as e:
                continue
    
    cur.close()
    conn.close()
    
    # Sort and return top results
    all_scored_docs.sort(key=lambda x: x['hybrid_score'], reverse=True)
    return [doc['content'] for doc in all_scored_docs[:top_k]]

# --- Ask Question to Ollama ---
def ask_ollama(question, context):
    prompt = f"""
    You are an expert assistant. Use the context below to answer the user's question. Don't recommend any other information other than the context provided.

    Context:
    {context}

    Question: {question}

    Answer:
    """
    response = chat(model="llama3.2", messages=[{"role": "user", "content": prompt}])
    return response['message']['content']

# --- MAIN PIPELINE ---
if __name__ == "__main__":
    # Initialize embedder
    embedder = NomicEmbedder()
    
    # Ask a question
    question = input("Enter your question: ")
    
    # Choose search method
    print("\nChoose search method:")
    print("1. Optimized Hybrid (Database pre-filtering)")
    print("2. Keyword-First Hybrid (PostgreSQL full-text + vector)")
    print("3. Pure pgvector similarity")
    print("4. Memory-efficient batch processing")
    choice = input("Enter choice (1-4): ").strip()
    
    start_time = time.time()
    start_datetime = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"Starting query processing at: {start_datetime}")
    
    if choice == "1":
        occurrence_weight = float(input("Enter occurrence weight (0-1, default 0.3): ") or "0.3")
        similarity_weight = 1.0 - occurrence_weight
        prefilter_size = int(input("Enter pre-filter size (default 100): ") or "100")
        print(f"Using optimized hybrid search (Occurrence: {occurrence_weight}, Similarity: {similarity_weight})")
        relevant_chunks = query_similar_chunks_hybrid_optimized(
            question, embedder, 
            occurrence_weight=occurrence_weight,
            similarity_weight=similarity_weight,
            prefilter_size=prefilter_size
        )
    elif choice == "2":
        print("Using keyword-first hybrid search")
        relevant_chunks = query_similar_chunks_keyword_first(question, embedder)
    elif choice == "3":
        print("Using pure pgvector similarity search")
        relevant_chunks = query_similar_chunks_pgvector(question, embedder)
    else:
        print("Using memory-efficient batch processing")
        relevant_chunks = query_similar_chunks_batch_optimized(question, embedder)
    
    context = "\n---\n".join(relevant_chunks)
    
    end_time = time.time()
    end_datetime = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"Finished processing at: {end_datetime}")
    print(f"Processing time: {end_time - start_time:.2f} seconds")
    
    answer = ask_ollama(question, context)
    
    print("\nAnswer from Ollama:\n")
    print(answer)