import os
from datetime import datetime
from llama_index.readers.file import PyMuPDFReader
from llama_index.vector_stores.postgres import PGVectorStore
from llama_index.core import Document, VectorStoreIndex, StorageContext
from transformers import AutoTokenizer, AutoModel
from llama_index.core.embeddings.base import BaseEmbedding
import torch

# --- Custom Nomic Embedder ---
class NomicEmbedding(BaseEmbedding):
    def __init__(self, model_name="nomic-ai/nomic-embed-text-v1"):
        self.tokenizer = AutoTokenizer.from_pretrained(model_name, trust_remote_code=True)
        self.model = AutoModel.from_pretrained(model_name, trust_remote_code=True)
        self.model.eval()

    def _get_embedding(self, text: str):
        with torch.no_grad():
            inputs = self.tokenizer(text, padding=True, truncation=True, return_tensors="pt")
            outputs = self.model(**inputs)
            return outputs.last_hidden_state[:, 0, :].squeeze(0).cpu().tolist()

    def _get_text_embeddings(self, texts):
        return [self._get_embedding(text) for text in texts]

# --- CONFIG ---
PDF_FOLDER = "diseases"
DB_NAME = "kforce"
DB_USER = "postgres"
DB_PASS = "kforce"
DB_HOST = "localhost"
DB_PORT = "5444"
DB_TABLE = "documents"

# --- Ensure folder exists ---
if not os.path.exists(PDF_FOLDER):
    os.makedirs(PDF_FOLDER)
    print(f"Created folder '{PDF_FOLDER}'")
    exit()

pdf_files = [f for f in os.listdir(PDF_FOLDER) if f.lower().endswith(".pdf")]
if not pdf_files:
    print("No PDF files found.")
    exit()

# --- Initialize components ---
embed_model = NomicEmbedding()
vector_store = PGVectorStore.from_params(
    database=DB_NAME,
    user=DB_USER,
    password=DB_PASS,
    host=DB_HOST,
    port=int(DB_PORT),
    table_name=DB_TABLE,
    embed_dim=768
)
storage_context = StorageContext.from_defaults(vector_store=vector_store)
reader = PyMuPDFReader()

# --- Load & Wrap Documents ---
all_docs = []
for i, pdf_file in enumerate(pdf_files, 1):
    path = os.path.join(PDF_FOLDER, pdf_file)
    print(f"[{i}/{len(pdf_files)}] Reading {pdf_file}")

    try:
        raw_docs = reader.load_data(file_path=path)
        filtered = [doc for doc in raw_docs if doc.text.strip()]
        wrapped = [Document(text=doc.text, metadata={"source": pdf_file}) for doc in filtered]
        all_docs.extend(wrapped)
        print(f"   ➜ Loaded {len(wrapped)} page(s)")
    except Exception as e:
        print(f"   ⚠️ Error: {e}")

if not all_docs:
    print("❌ No valid documents found. Exiting.")
    exit()

# --- Index into pgvector ---
print(f"\n📡 Embedding and storing {len(all_docs)} documents...")
index = VectorStoreIndex(
    all_docs,
    embed_model=embed_model,
    storage_context=storage_context,
)

print(f"\n✅ Done. Indexed {len(all_docs)} documents.")
