#!/usr/bin/env python3
"""
Analyze PDF structure to create better chunking strategy
"""

import fitz
import random
import re
from pathlib import Path
from collections import Counter

def analyze_single_pdf(pdf_path):
    """Analyze structure of a single PDF"""
    
    try:
        doc = fitz.open(pdf_path)
        text = ""
        for page in doc:
            text += page.get_text()
        doc.close()
        
        lines = [line.strip() for line in text.split('\n') if line.strip()]
        
        analysis = {
            'filename': pdf_path.name,
            'total_lines': len(lines),
            'lines': lines,
            'structure_patterns': [],
            'sections': []
        }
        
        # Look for common patterns
        for i, line in enumerate(lines):
            # Check for headings (all caps, short lines)
            if line.isupper() and len(line) < 50 and len(line) > 3:
                analysis['structure_patterns'].append(('HEADING', i, line))
            
            # Check for numbered sections
            if re.match(r'^\d+\.', line):
                analysis['structure_patterns'].append(('NUMBERED', i, line))
            
            # Check for bullet points
            if line.startswith('•') or line.startswith('-') or line.startswith('*'):
                analysis['structure_patterns'].append(('BULLET', i, line))
            
            # Check for section indicators
            section_keywords = ['symptoms', 'treatment', 'diagnosis', 'occurrence', 'prevention', 'causes', 'etiology']
            if any(keyword in line.lower() for keyword in section_keywords):
                analysis['structure_patterns'].append(('SECTION', i, line))
        
        return analysis
        
    except Exception as e:
        return {'filename': pdf_path.name, 'error': str(e)}

def find_common_patterns(analyses):
    """Find common structural patterns across PDFs"""
    
    all_patterns = []
    section_indicators = []
    
    for analysis in analyses:
        if 'error' in analysis:
            continue
            
        for pattern_type, line_num, text in analysis.get('structure_patterns', []):
            all_patterns.append(pattern_type)
            if pattern_type in ['HEADING', 'SECTION']:
                section_indicators.append(text.lower())
    
    pattern_counts = Counter(all_patterns)
    common_sections = Counter(section_indicators)
    
    return pattern_counts, common_sections

def create_chunking_strategy(analyses):
    """Create improved chunking strategy based on analysis"""
    
    pattern_counts, common_sections = find_common_patterns(analyses)
    
    print("📊 Analysis Results:")
    print("=" * 40)
    print(f"Pattern frequencies: {dict(pattern_counts)}")
    print(f"Common sections: {dict(common_sections.most_common(10))}")
    
    # Define improved chunking rules
    chunking_rules = {
        'section_keywords': [
            'symptoms', 'treatment', 'diagnosis', 'occurrence', 'prevention', 
            'causes', 'etiology', 'pathogenesis', 'prognosis', 'overview',
            'introduction', 'clinical signs', 'therapy', 'management'
        ],
        'heading_patterns': [
            r'^[A-Z][A-Z\s]{3,}$',  # All caps headings
            r'^\d+\.\s*[A-Z]',       # Numbered sections
            r'^##?\s*[A-Z]',         # Markdown style
        ],
        'bullet_patterns': [
            r'^[•\-\*]\s+',          # Bullet points
            r'^\d+\)\s+',            # Numbered lists
        ]
    }
    
    return chunking_rules

def main():
    """Main analysis function"""
    
    print("🔍 PDF Structure Analysis for Improved Chunking")
    print("=" * 60)
    
    # Get random 10 PDFs
    pdf_folder = Path('diseases')
    all_pdfs = list(pdf_folder.glob('*.pdf'))
    sample_pdfs = random.sample(all_pdfs, min(10, len(all_pdfs)))
    
    print(f"📄 Analyzing {len(sample_pdfs)} random PDFs:")
    for pdf in sample_pdfs:
        print(f"  • {pdf.name}")
    
    # Analyze each PDF
    analyses = []
    for pdf_path in sample_pdfs:
        print(f"\n🔍 Analyzing {pdf_path.name}...")
        analysis = analyze_single_pdf(pdf_path)
        analyses.append(analysis)
        
        if 'error' not in analysis:
            print(f"  Lines: {analysis['total_lines']}")
            print(f"  Patterns found: {len(analysis['structure_patterns'])}")
            
            # Show first few lines for structure understanding
            print("  First 10 lines:")
            for i, line in enumerate(analysis['lines'][:10], 1):
                print(f"    {i:2d}. {line}")
        else:
            print(f"  Error: {analysis['error']}")
    
    # Create improved chunking strategy
    print(f"\n🧠 Creating Improved Chunking Strategy...")
    chunking_rules = create_chunking_strategy(analyses)
    
    print(f"\n📋 Recommended Chunking Rules:")
    print("=" * 40)
    print("Section Keywords:", chunking_rules['section_keywords'])
    print("Heading Patterns:", chunking_rules['heading_patterns'])
    print("Bullet Patterns:", chunking_rules['bullet_patterns'])
    
    return analyses, chunking_rules

if __name__ == "__main__":
    analyses, rules = main()
